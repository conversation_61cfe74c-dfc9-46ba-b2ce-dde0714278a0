"use client"

import type React from "react"

import posthog from "posthog-js"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON> } from "posthog-js/react"
import { useEffect, useState } from "react"
import { useTheme } from "next-themes"

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  const [isInitialized, setIsInitialized] = useState(false)
  const { theme, systemTheme } = useTheme()
  const currentTheme = theme === "system" ? systemTheme : theme

  useEffect(() => {
    const posthogKey = process.env.NEXT_PUBLIC_POSTHOG_KEY
    console.log("🔍 PostHog Provider: Starting initialization...")
    console.log("🔍 PostHog Key:", posthogKey ? "Set" : "Not set")
    console.log("🔍 Window available:", typeof window !== "undefined")
    console.log("🔍 Current theme:", currentTheme)

    if (posthogKey && typeof window !== "undefined") {
      // Initialize PostHog immediately, don't wait for theme
      console.log("🔍 PostHog Provider: Initializing PostHog...")

      try {
        posthog.init(posthogKey, {
          api_host: "/ingest",
          ui_host: "https://us.posthog.com",
          defaults: "2025-05-24",
          capture_exceptions: true,
          debug: process.env.NODE_ENV === "development",
          // Enable Session Replay with privacy protection
          session_recording: {
            maskAllInputs: true, // Mask all input fields for privacy
            maskInputOptions: {
              password: true, // Always mask passwords
              email: true, // Mask email addresses
              tel: true, // Mask phone numbers
            },
          },
          // Additional privacy and performance settings
          autocapture: true, // Automatically capture clicks, form submissions, etc.
          capture_pageview: true, // Capture page views
          capture_pageleave: true, // Capture when users leave pages
          disable_session_recording: false, // Ensure session recording is enabled
          // Theme-aware configuration
          loaded: (posthog) => {
            console.log("✅ PostHog Provider: PostHog loaded successfully")
            // Set user properties for theme tracking
            if (currentTheme) {
              posthog.people.set({
                theme: currentTheme,
                theme_preference: theme === "system" ? "system" : "manual",
              })
              console.log("✅ PostHog Provider: Theme properties set:", {
                theme: currentTheme,
                theme_preference: theme === "system" ? "system" : "manual",
              })
            }
          },
        })

        console.log("✅ PostHog Provider: PostHog initialized successfully")
        setIsInitialized(true)
      } catch (error) {
        console.error("❌ PostHog Provider: Initialization failed:", error)
        // Still set as initialized to avoid blocking the app
        setIsInitialized(true)
      }
    } else {
      console.warn("⚠️ PostHog Provider: Missing PostHog key or window not available")
      // Set as initialized to avoid blocking the app
      setIsInitialized(true)
    }
  }, []) // Remove currentTheme dependency to initialize immediately

  // Update PostHog theme when it changes
  useEffect(() => {
    if (isInitialized && currentTheme && typeof posthog !== "undefined" && posthog.people) {
      try {
        posthog.people.set({
          theme: currentTheme,
          theme_preference: theme === "system" ? "system" : "manual",
        })
        console.log("✅ PostHog Provider: Theme updated:", {
          theme: currentTheme,
          theme_preference: theme === "system" ? "system" : "manual",
        })
      } catch (error) {
        console.error("❌ PostHog Provider: Failed to update theme:", error)
      }
    }
  }, [currentTheme, isInitialized, theme])

  // Only render children after PostHog is initialized to avoid hydration issues
  if (!isInitialized) {
    console.log("⏳ PostHog Provider: Waiting for initialization...")
    return <>{children}</>
  }

  console.log("✅ PostHog Provider: Rendering with PostHog context")
  return <PHProvider client={posthog}>{children}</PHProvider>
}
