import posthog from "posthog-js"
import { platformConfig } from "./platform-config"

// Helper function to check if PostHog is ready
const isPostHogReady = () => {
  return typeof posthog !== "undefined" && posthog.isFeatureEnabled !== undefined
}

const getBaseEventData = () => ({
  project_name: platformConfig.variables.projectName,
  project_id: platformConfig.metadata.id,
  company: platformConfig.branding.companyName,
  timestamp: new Date().toISOString(),
})

// Analytics event tracking for PEP Portal
export const analytics = {
  // Section Navigation Tracking
  trackSectionView: (sectionId: number, sectionTitle: string, sectionPhase: string, macroSection?: string) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      section_id: sectionId,
      section_title: sectionTitle,
      section_phase: sectionPhase,
      macro_section: macroSection || "unknown",
    }

    console.log("🔍 Tracking section_viewed:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("section_viewed", eventData)
        console.log("✅ PostHog event captured: section_viewed")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackSectionNavigation: (
    fromSection: number,
    toSection: number,
    navigationType: "next" | "previous" | "direct",
    fromMacroSection?: string,
    toMacroSection?: string,
  ) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      from_section: fromSection,
      to_section: toSection,
      navigation_type: navigationType,
      from_macro_section: fromMacroSection || "unknown",
      to_macro_section: toMacroSection || "unknown",
    }

    console.log("🔍 Tracking section_navigation:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("section_navigation", eventData)
        console.log("✅ PostHog event captured: section_navigation")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  // Sidebar Interaction Tracking
  trackSidebarToggle: (macroSection: string, action: "expand" | "collapse") => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      macro_section: macroSection,
      action: action,
    }

    console.log("🔍 Tracking sidebar_toggle:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("sidebar_toggle", eventData)
        console.log("✅ PostHog event captured: sidebar_toggle")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackSidebarSectionClick: (sectionId: number, sectionTitle: string, macroSection: string) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      section_id: sectionId,
      section_title: sectionTitle,
      macro_section: macroSection,
    }

    console.log("🔍 Tracking sidebar_section_click:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("sidebar_section_click", eventData)
        console.log("✅ PostHog event captured: sidebar_section_click")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  // Chatbot Interaction Tracking
  trackChatbotQuestion: (
    question: string,
    questionType: "manual_input" | "suggested_question",
    activeSection: number,
  ) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      question: question,
      question_type: questionType,
      active_section: activeSection,
      question_length: question.length,
    }

    console.log("🔍 Tracking chatbot_question_asked:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("chatbot_question_asked", eventData)
        console.log("✅ PostHog event captured: chatbot_question_asked")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackChatbotResponse: (question: string, responseLength: number, activeSection: number, responseTime: number) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      question: question,
      response_length: responseLength,
      active_section: activeSection,
      response_time_ms: responseTime,
    }

    console.log("🔍 Tracking chatbot_response_received:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("chatbot_response_received", eventData)
        console.log("✅ PostHog event captured: chatbot_response_received")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackChatbotSectionReference: (referencedSection: number, referencedTitle: string, activeSection: number) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      referenced_section: referencedSection,
      referenced_title: referencedTitle,
      active_section: activeSection,
    }

    console.log("🔍 Tracking chatbot_section_reference:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("chatbot_section_reference", eventData)
        console.log("✅ PostHog event captured: chatbot_section_reference")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  // User Engagement Tracking
  trackPageInteraction: (interactionType: string, elementId?: string, elementType?: string, sectionId?: number) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      interaction_type: interactionType,
      element_id: elementId,
      element_type: elementType,
      section_id: sectionId,
    }

    console.log("🔍 Tracking page_interaction:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("page_interaction", eventData)
        console.log("✅ PostHog event captured: page_interaction")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackButtonClick: (buttonText: string, buttonLocation: string, sectionId?: number) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      button_text: buttonText,
      button_location: buttonLocation,
      section_id: sectionId,
    }

    console.log("🔍 Tracking button_clicked:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("button_clicked", eventData)
        console.log("✅ PostHog event captured: button_clicked")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  trackProposalCTA: (sectionTitle: string, action: "view" | "click") => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      section_title: sectionTitle,
      action: action,
    }

    console.log("🔍 Tracking proposal_cta_interaction:", eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture("proposal_cta_interaction", eventData)
        console.log("✅ PostHog event captured: proposal_cta_interaction")
      } catch (error) {
        console.error("❌ PostHog capture error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, event not captured:", eventData)
    }
  },

  // Session and User Properties
  setUserProperties: (properties: Record<string, any>) => {
    const enrichedProperties = {
      ...properties,
      project_name: platformConfig.variables.projectName,
      project_id: platformConfig.metadata.id,
      company: platformConfig.branding.companyName,
    }

    if (isPostHogReady()) {
      try {
        posthog.people.set(enrichedProperties)
        console.log("✅ PostHog user properties set:", enrichedProperties)
      } catch (error) {
        console.error("❌ PostHog setUserProperties error:", error)
      }
    } else {
      console.warn("⚠️ PostHog not ready, user properties not set:", enrichedProperties)
    }
  },

  // Custom Event with Flexible Properties
  trackCustomEvent: (eventName: string, properties: Record<string, any>) => {
    const eventData = {
      ...getBaseEventData(), // Add project name and common properties
      ...properties,
    }

    console.log(`🔍 Tracking custom event ${eventName}:`, eventData)

    if (isPostHogReady()) {
      try {
        posthog.capture(eventName, eventData)
        console.log(`✅ PostHog event captured: ${eventName}`)
      } catch (error) {
        console.error(`❌ PostHog capture error for ${eventName}:`, error)
      }
    } else {
      console.warn(`⚠️ PostHog not ready, event ${eventName} not captured:`, eventData)
    }
  },

  // Debug function to check PostHog status
  debug: () => {
    console.log("🔍 PostHog Debug Info:")
    console.log("- PostHog object:", posthog)
    console.log("- Is ready:", isPostHogReady())
    console.log("- Environment:", process.env.NODE_ENV)
    console.log("- PostHog key:", process.env.NEXT_PUBLIC_POSTHOG_KEY ? "Set" : "Not set")
    console.log("- Project name:", platformConfig.variables.projectName)
    console.log("- Project ID:", platformConfig.metadata.id)
  },
}

// Hook for tracking component interactions
export const useAnalytics = () => {
  return {
    track: analytics.trackCustomEvent,
    trackSectionView: analytics.trackSectionView,
    trackButtonClick: analytics.trackButtonClick,
    trackChatbotQuestion: analytics.trackChatbotQuestion,
    debug: analytics.debug,
  }
}
