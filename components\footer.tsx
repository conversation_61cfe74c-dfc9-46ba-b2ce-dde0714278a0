import { platformConfig } from "@/lib/platform-config"

export function Footer() {
  const year = new Date().getFullYear()
  const company = platformConfig.branding.companyDescription

  return (
    <footer className="bg-background border-t border-border mt-auto">
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-6">
          <div>
            <h3 className="text-sm font-semibold text-foreground mb-4">Legal</h3>
            <div className="space-y-2">
              <a
                href="#"
                className="block text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="block text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                Terms and Conditions
              </a>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-foreground mb-4">Disclaimer</h3>
            <p className="text-sm text-muted-foreground leading-relaxed">
              The entire proposal content is based on customer-provided input and estimations made by a singular team.
              All projections, timelines, and technical specifications are preliminary estimates subject to change based on
              detailed requirements analysis and market conditions.
            </p>
          </div>
        </div>

        <div className="pt-6 border-t border-border">
          <p className="text-center text-sm text-muted-foreground">
            © {year} {company}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
