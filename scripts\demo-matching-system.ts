import { MatchingAlgorithms, type UserProfile } from "../lib/matching-algorithms"

console.log("🚀 Demo: 9-Algorithm Matching System")
console.log("=====================================")

// Sample user profiles for demonstration
const users: UserProfile[] = [
  {
    id: "1",
    name: "<PERSON>",
    prompt: "I love hiking and camping in the mountains every weekend.",
    interests: ["hiking", "camping", "nature"],
    values: ["sustainability", "adventure"],
    traits: ["spontaneous", "outdoorsy"],
    offers: ["hiking guidance"],
    seeks: ["camping partners"],
  },
  {
    id: "2",
    name: "<PERSON>",
    prompt: "Mountain trails and nature exploration bring me peace.",
    interests: ["nature", "photography", "meditation"],
    values: ["mindfulness", "conservation"],
    traits: ["contemplative", "artistic"],
    offers: ["photography tips"],
    seeks: ["meditation partners"],
  },
  {
    id: "3",
    name: "<PERSON>",
    prompt: "I'm fascinated by artificial intelligence and its impact on jobs.",
    interests: ["technology", "AI", "future of work"],
    values: ["innovation", "learning"],
    traits: ["analytical", "curious"],
    offers: ["tech insights"],
    seeks: ["discussion partners"],
  },
  {
    id: "4",
    name: "<PERSON>",
    prompt: "I can help you learn beginner guitar—ask me anything.",
    interests: ["music", "teaching", "guitar"],
    values: ["sharing knowledge", "creativity"],
    traits: ["patient", "musical"],
    offers: ["guitar lessons"],
    seeks: ["music collaboration"],
  },
  {
    id: "5",
    name: "Riley",
    prompt: "Looking for someone to teach me basic guitar chords.",
    interests: ["music", "learning", "creativity"],
    values: ["growth", "artistic expression"],
    traits: ["eager learner", "creative"],
    offers: ["enthusiasm"],
    seeks: ["guitar instruction"],
  },
]

// Demo each algorithm
console.log("\n🎯 1. Keyword Matching Demo")
console.log('Alex: "I love hiking and camping"')
console.log('Sam: "Mountain trails and nature exploration"')
const keywordMatch = MatchingAlgorithms.keywordMatching(users[0], users[1])
console.log(`Match Score: ${keywordMatch.score}% - ${keywordMatch.reasoning}`)

console.log("\n🌍 2. Context Matching Demo")
const contextMatch = MatchingAlgorithms.contextMatching(users[0], users[1])
console.log(`Match Score: ${contextMatch.score}% - ${contextMatch.reasoning}`)

console.log("\n❤️ 3. Simple Interests Matching Demo")
const interestsMatch = MatchingAlgorithms.simpleInterestsMatching(users[2], users[3])
console.log(`Match Score: ${interestsMatch.score}% - ${interestsMatch.reasoning}`)

console.log("\n🧠 4. Semantic Understanding Demo")
const semanticMatch = MatchingAlgorithms.semanticMatching(users[0], users[1])
console.log(`Match Score: ${semanticMatch.score}% - ${semanticMatch.reasoning}`)

console.log("\n💬 5. Conversation Potential Demo")
const conversationMatch = MatchingAlgorithms.conversationPotentialMatching(users[2], users[2])
console.log(`Match Score: ${conversationMatch.score}% - ${conversationMatch.reasoning}`)

console.log("\n🤝 6. Shared Values/Beliefs Demo")
const valuesMatch = MatchingAlgorithms.valuesMatching(users[0], users[1])
console.log(`Match Score: ${valuesMatch.score}% - ${valuesMatch.reasoning}`)

console.log("\n🧩 7. Complementary Traits Demo")
const complementaryMatch = MatchingAlgorithms.complementaryTraitMatching(users[0], users[1])
console.log(`Match Score: ${complementaryMatch.score}% - ${complementaryMatch.reasoning}`)

console.log("\n🎁 8. Reciprocal Needs Demo")
console.log('Casey: "I can help you learn beginner guitar"')
console.log('Riley: "Looking for someone to teach me basic guitar chords"')
const reciprocalMatch = MatchingAlgorithms.reciprocalNeedsMatching(users[3], users[4])
console.log(`Match Score: ${reciprocalMatch.score}% - ${reciprocalMatch.reasoning}`)

console.log("\n🤖 9. AI-Predicted/Behavioral Demo")
const behavioralMatch = MatchingAlgorithms.behavioralMatching(users[0], users[1])
console.log(`Match Score: ${behavioralMatch.score}% - ${behavioralMatch.reasoning}`)

console.log("\n🔍 Complete Matching Analysis")
console.log("Finding all matches for Alex...")
const allMatches = MatchingAlgorithms.findMatches(users[0], users.slice(1))
console.log(`Found ${allMatches.length} total matches across all algorithms`)

allMatches.slice(0, 5).forEach((match, index) => {
  const matchedUser = users.find((u) => u.id === match.userId)
  console.log(
    `${index + 1}. ${matchedUser?.name} - ${match.matchType}: ${Math.round(match.score)}% (${Math.round(match.confidence * 100)}% confidence)`,
  )
})

console.log("\n✅ Demo completed successfully!")
console.log("The matching system is ready for production use.")
