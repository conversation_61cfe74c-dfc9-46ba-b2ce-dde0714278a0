"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { MatchingAlgorithms, type UserProfile, type MatchResult } from "@/lib/matching-algorithms"
import { Heart, Users, MessageCircle, Target, Lightbulb, Handshake, Puzzle, Gift, Brain } from "lucide-react"

const algorithmIcons = {
  "Keyword Matching": Target,
  "Context Matching": Users,
  "Simple Interests Matching": Heart,
  "Semantic Understanding": Brain,
  "Conversation Potential": MessageCircle,
  "Shared Values/Beliefs": Handshake,
  "Complementary Traits": <PERSON>uzzle,
  "Reciprocal Needs": Gift,
  "AI-Predicted/Behavioral": Lightbulb,
}

const sampleUsers: UserProfile[] = [
  {
    id: "1",
    name: "<PERSON>",
    prompt: "I love hiking and camping in the mountains every weekend.",
    interests: ["hiking", "camping", "nature"],
    values: ["sustainability", "adventure"],
    traits: ["spontaneous", "outdoorsy"],
    offers: ["hiking guidance"],
    seeks: ["camping partners"],
  },
  {
    id: "2",
    name: "Sam",
    prompt: "Mountain trails and nature exploration bring me peace.",
    interests: ["nature", "photography", "meditation"],
    values: ["mindfulness", "conservation"],
    traits: ["contemplative", "artistic"],
    offers: ["photography tips"],
    seeks: ["meditation partners"],
  },
  {
    id: "3",
    name: "Jordan",
    prompt: "I'm fascinated by artificial intelligence and its impact on jobs.",
    interests: ["technology", "AI", "future of work"],
    values: ["innovation", "learning"],
    traits: ["analytical", "curious"],
    offers: ["tech insights"],
    seeks: ["discussion partners"],
  },
  {
    id: "4",
    name: "Casey",
    prompt: "I can help you learn beginner guitar—ask me anything.",
    interests: ["music", "teaching", "guitar"],
    values: ["sharing knowledge", "creativity"],
    traits: ["patient", "musical"],
    offers: ["guitar lessons"],
    seeks: ["music collaboration"],
  },
  {
    id: "5",
    name: "Riley",
    prompt: "Looking for someone to teach me basic guitar chords.",
    interests: ["music", "learning", "creativity"],
    values: ["growth", "artistic expression"],
    traits: ["eager learner", "creative"],
    offers: ["enthusiasm"],
    seeks: ["guitar instruction"],
  },
]

export function MatchingInterface() {
  const [currentUser, setCurrentUser] = useState<UserProfile>({
    id: "current",
    name: "You",
    prompt: "",
    interests: [],
    values: [],
    traits: [],
    offers: [],
    seeks: [],
  })

  const [matches, setMatches] = useState<MatchResult[]>([])
  const [selectedAlgorithm, setSelectedAlgorithm] = useState<string>("all")
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const handleAnalyze = async () => {
    if (!currentUser.prompt.trim()) return

    setIsAnalyzing(true)

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1500))

    const allMatches = MatchingAlgorithms.findMatches(currentUser, sampleUsers)
    setMatches(allMatches)
    setIsAnalyzing(false)
  }

  const filteredMatches =
    selectedAlgorithm === "all" ? matches : matches.filter((match) => match.matchType === selectedAlgorithm)

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const getScoreBadgeVariant = (score: number) => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "outline"
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-balance">Smart Matching System</h1>
        <p className="text-muted-foreground text-pretty max-w-2xl mx-auto">
          Experience our advanced 9-algorithm matching system that finds meaningful connections through keyword
          analysis, semantic understanding, behavioral prediction, and more.
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Create Your Profile</CardTitle>
          <CardDescription>
            Tell us about yourself to find the best matches using our 9 different algorithms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Your Name</Label>
            <Input
              id="name"
              value={currentUser.name}
              onChange={(e) => setCurrentUser((prev) => ({ ...prev, name: e.target.value }))}
              placeholder="Enter your name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt">Your Prompt</Label>
            <Textarea
              id="prompt"
              value={currentUser.prompt}
              onChange={(e) => setCurrentUser((prev) => ({ ...prev, prompt: e.target.value }))}
              placeholder="Describe yourself, your interests, what you're looking for..."
              className="min-h-[100px]"
            />
          </div>

          <Button onClick={handleAnalyze} disabled={!currentUser.prompt.trim() || isAnalyzing} className="w-full">
            {isAnalyzing ? "Analyzing with 9 Algorithms..." : "Find My Matches"}
          </Button>
        </CardContent>
      </Card>

      {matches.length > 0 && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-semibold">Your Matches</h2>
            <Badge variant="outline">{matches.length} total matches found</Badge>
          </div>

          <Tabs value={selectedAlgorithm} onValueChange={setSelectedAlgorithm}>
            <TabsList className="grid grid-cols-5 lg:grid-cols-10 gap-1">
              <TabsTrigger value="all" className="text-xs">
                All
              </TabsTrigger>
              {Object.keys(algorithmIcons).map((algorithm) => (
                <TabsTrigger key={algorithm} value={algorithm} className="text-xs">
                  {algorithm.split(" ")[0]}
                </TabsTrigger>
              ))}
            </TabsList>

            <TabsContent value={selectedAlgorithm} className="space-y-4">
              <div className="grid gap-4">
                {filteredMatches.map((match, index) => {
                  const user = sampleUsers.find((u) => u.id === match.userId)
                  const IconComponent = algorithmIcons[match.matchType as keyof typeof algorithmIcons]

                  return (
                    <Card key={`${match.userId}-${match.matchType}-${index}`}>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <div>
                              <CardTitle className="text-lg">{user?.name}</CardTitle>
                              <CardDescription>{match.matchType}</CardDescription>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getScoreBadgeVariant(match.score)}>{Math.round(match.score)}% match</Badge>
                            <Badge variant="outline">{Math.round(match.confidence * 100)}% confidence</Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <p className="text-sm text-muted-foreground">
                          <strong>Their prompt:</strong> "{user?.prompt}"
                        </p>

                        <div className="p-3 bg-muted rounded-lg">
                          <p className="text-sm font-medium text-primary mb-1">Why you match:</p>
                          <p className="text-sm">{match.reasoning}</p>
                        </div>

                        {match.details && (
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(match.details).map(([key, value]) => (
                              <Badge key={key} variant="secondary" className="text-xs">
                                {key.replace(/([A-Z])/g, " $1").toLowerCase()}: {Math.round((value as number) * 100)}%
                              </Badge>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      )}

      {matches.length === 0 && currentUser.prompt && !isAnalyzing && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground">
              Click "Find My Matches" to discover connections using our 9 advanced algorithms
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
