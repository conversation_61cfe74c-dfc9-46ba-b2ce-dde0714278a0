export function processTemplate(template: string, variables: Record<string, string>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key] || match
  })
}

export function processContent(content: string, variables: Record<string, string>): string {
  return processTemplate(content, variables)
}

export function processObject<T>(obj: T, variables: Record<string, string>): T {
  if (typeof obj === "string") {
    return processTemplate(obj, variables) as T
  }

  if (Array.isArray(obj)) {
    return obj.map((item) => processObject(item, variables)) as T
  }

  if (obj && typeof obj === "object") {
    const processed: any = {}
    for (const [key, value] of Object.entries(obj)) {
      processed[key] = processObject(value, variables)
    }
    return processed as T
  }

  return obj
}
