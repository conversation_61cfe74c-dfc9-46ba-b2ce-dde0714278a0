"use client"

import { useState, useEffect } from "react"
import { PepSidebar } from "@/components/pep-sidebar"
import { PepContent } from "@/components/pep-content"
import { PepHome } from "@/components/pep-home"
import { AiChatbot } from "@/components/ai-chatbot"
import { Footer } from "@/components/footer"
import { Providers } from "@/components/providers"
import { analytics } from "@/lib/analytics"

export default function Home() {
  const [activeSection, setActiveSection] = useState(0)

  // Debug PostHog status on mount
  useEffect(() => {
    // Small delay to ensure PostHog has time to initialize
    const timer = setTimeout(() => {
      console.log("🔍 Debugging PostHog status...")
      analytics.debug()
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  const handleSectionChange = (sectionId: number) => {
    console.log(`🔄 Section change: ${activeSection} -> ${sectionId}`)
    setActiveSection(sectionId)
  }

  return (
    <Providers>
      <div className="flex flex-col min-h-screen">
        <div className="flex flex-1">
          <PepSidebar activeSection={activeSection} onSectionChange={handleSectionChange} />
          {activeSection === 0 ? (
            <PepHome onSectionChange={handleSectionChange} />
          ) : (
            <PepContent activeSection={activeSection} onSectionChange={handleSectionChange} />
          )}
          <div className="fixed bottom-4 right-4 z-50">
            <AiChatbot activeSection={activeSection} onSectionChange={handleSectionChange} />
          </div>
        </div>
        <Footer />
      </div>
    </Providers>
  )
}
