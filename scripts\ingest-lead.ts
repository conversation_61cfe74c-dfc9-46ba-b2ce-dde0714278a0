import fs from 'fs'
import path from 'path'
import { processObject } from '../lib/template-engine'

interface Schema {
  type: string
  properties?: Record<string, any>
  items?: any
  required?: string[]
  additionalProperties?: boolean
  format?: string
}

function loadJSON(filePath: string) {
  return JSON.parse(fs.readFileSync(filePath, 'utf8'))
}

function validate(data: any, schema: Schema, path: string = ''): string[] {
  const errors: string[] = []

  if (schema.type === 'object') {
    if (typeof data !== 'object' || data === null || Array.isArray(data)) {
      errors.push(`${path || 'data'} should be object`)
      return errors
    }
    const { properties = {}, required = [], additionalProperties } = schema
    for (const req of required) {
      if (!(req in data)) errors.push(`${path ? `${path}.` : ''}${req} is required`)
    }
    for (const [key, value] of Object.entries(data)) {
      const propSchema = properties[key]
      if (!propSchema) {
        if (additionalProperties === false) {
          errors.push(`${path ? `${path}.` : ''}${key} is not allowed`)
        }
        continue
      }
      errors.push(...validate(value, propSchema, path ? `${path}.${key}` : key))
    }
    return errors
  }

  if (schema.type === 'array') {
    if (!Array.isArray(data)) {
      errors.push(`${path || 'data'} should be array`)
      return errors
    }
    if (schema.items) {
      data.forEach((item, idx) => {
        errors.push(...validate(item, schema.items as Schema, `${path}[${idx}]`))
      })
    }
    return errors
  }

  if (typeof data !== schema.type) {
    errors.push(`${path || 'data'} should be ${schema.type}`)
  }
  if (schema.format === 'email') {
    const emailRegex = /^[^@\s]+@[^@\s]+\.[^@\s]+$/
    if (typeof data !== 'string' || !emailRegex.test(data)) {
      errors.push(`${path || 'data'} should match email format`)
    }
  }
  if (schema.format === 'date') {
    if (typeof data !== 'string' || isNaN(Date.parse(data))) {
      errors.push(`${path || 'data'} should match date format`)
    }
  }
  if (schema.format === 'uri') {
    try {
      if (typeof data !== 'string') throw new Error('')
      new URL(data)
    } catch {
      errors.push(`${path || 'data'} should be a valid URI`)
    }
  }

  return errors
}

function collectVariables(obj: any, vars: Record<string, string> = {}): Record<string, string> {
  if (obj && typeof obj === 'object') {
    for (const [k, v] of Object.entries(obj)) {
      if (v && typeof v === 'object') {
        collectVariables(v, vars)
      } else if (typeof v !== 'function') {
        const value = String(v)
        if (!value.includes('{{')) vars[k] = value
      }
    }
  }
  return vars
}

function main() {
  const [, , inputPath] = process.argv
  if (!inputPath) {
    console.error('Usage: npm run ingest -- <path-to-lead-json>')
    process.exit(1)
  }
  const leadPath = path.resolve(inputPath)
  const rootDir = process.cwd()
  const schemaPath = path.resolve(rootDir, 'scripts', 'lead-config.schema.json')

  const leadData = loadJSON(leadPath)
  const schema: Schema = loadJSON(schemaPath)

  const errors = validate(leadData, schema)
  if (errors.length) {
    console.error('Lead config validation failed:')
    for (const err of errors) console.error(' -', err)
    process.exit(1)
  }

  const variables = collectVariables(leadData)
  const processed = processObject(leadData, variables)

  const outputPath = path.resolve(rootDir, 'lib', 'platform-config.ts')
  const contents = `export const platformConfig = ${JSON.stringify(processed, null, 2)}\n`
  fs.writeFileSync(outputPath, contents)
  console.log(`Wrote ${outputPath}`)
}

main()
