import { platformConfig } from "./platform-config"
import { processObject } from "./template-engine"

// Process the entire config with variable substitution
export const processedConfig = processObject(platformConfig, platformConfig.variables)

// Export processed sections for components
export const macroSections = processedConfig.macroSections
export const content = processedConfig.content
export const branding = processedConfig.branding

// Legacy compatibility - convert to old format for existing components
export const pepSections = macroSections.flatMap((macro) =>
  macro.sections.map((section) => ({
    id: section.id, // Keep as number for compatibility
    section_key: section.section_key,
    title: section.title,
    phase: section.phase,
    content: section.content,
  })),
)

// Helper function to get macro section for a section ID
export function getMacroSectionForSection(sectionId: number): string {
  const macro = macroSections.find((m) => m.sections.some((s) => s.id === sectionId))
  return macro?.id || "unknown"
}

// Helper to get icon component from config
export function getIconComponent(iconConfig: { name: string }) {
  // This would normally import from lucide-react dynamically
  // For now, return the name for the components to handle
  return iconConfig.name
}
