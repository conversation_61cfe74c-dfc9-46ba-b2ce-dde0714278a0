
interface Options {
  id: string
  title: string
  sectionCount: number
  startSectionId: number
  order: number
}

function parseArgs(): Options {
  const args = process.argv.slice(2)
  const opts: any = { sectionCount: 1, startSectionId: 1, order: 1 }
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--id':
        opts.id = args[++i]
        break
      case '--title':
        opts.title = args[++i]
        break
      case '--section-count':
        opts.sectionCount = parseInt(args[++i], 10)
        break
      case '--start-id':
        opts.startSectionId = parseInt(args[++i], 10)
        break
      case '--order':
        opts.order = parseInt(args[++i], 10)
        break
    }
  }
  if (!opts.id || !opts.title) {
    console.error('Usage: npm run generate-macro -- --id <macro-id> --title <title> [--section-count <n>] [--start-id <m>] [--order <o>]')
    process.exit(1)
  }
  return opts as Options
}

function generate(opts: Options) {
  const sections = Array.from({ length: opts.sectionCount }, (_, idx) => ({
    id: opts.startSectionId + idx,
    section_key: '',
    title: '',
    icon: { name: '' },
    phase: '',
    content: '',
    info_card_title: '',
    info_card_content: '',
    order_index: idx + 1,
  }))
  const macroSection = {
    id: opts.id,
    title: opts.title,
    icon: { name: '' },
    description: '',
    sections,
    order: opts.order,
  }
  console.log(JSON.stringify(macroSection, null, 2))
}

const options = parseArgs()
generate(options)
