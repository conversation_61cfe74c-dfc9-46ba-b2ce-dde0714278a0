"use client"
import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, Info } from "lucide-react"
import { analytics } from "@/lib/analytics"
import { useEffect } from "react"
import {
  pepSections,
  getMacroSectionForSection,
  macroSections,
  content,
} from "@/lib/platform-data"
import { MatchingInterface } from "@/components/matching-interface"
import { SubscriptionTierSelector } from "@/components/subscription-tier-selector"

interface PepContentProps {
  activeSection: number
  onSectionChange: (section: number) => void
}

const ProposalCTA = ({ sectionTitle, onProposalClick }: { sectionTitle: string; onProposalClick: () => void }) => (
  <div className="mt-10 p-8 bg-gradient-to-r from-orange-50 to-cyan-50 dark:from-orange-950/20 dark:to-cyan-950/20 rounded-2xl border border-orange-200 dark:border-orange-800 shadow-sm">
    <div className="flex items-center justify-between">
      <div className="space-y-2">
        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
          Ready to move forward with {sectionTitle}?
        </h4>
        <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
          Let's discuss how Singular Agency can bring this vision to life for your business.
        </p>
      </div>
      <Button
        className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
        onClick={onProposalClick}
      >
        View Proposal
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
    </div>
  </div>
)

const PepContent = ({ activeSection, onSectionChange }: PepContentProps) => {
  const sections = pepSections

  // Track section view when component mounts or section changes
  useEffect(() => {
    if (activeSection > 0) {
      const currentSection = sections.find((section) => section.id === activeSection)
      if (currentSection) {
        const macroSection = getMacroSectionForSection(activeSection)
        analytics.trackSectionView(activeSection, currentSection.title, currentSection.phase, macroSection)
      }
    }
  }, [activeSection, sections])

  const currentSection = sections.find((section) => section.id === activeSection)
  if (!currentSection) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600 dark:text-gray-400">Section not found</p>
      </div>
    )
  }

  const isMatchingSection = currentSection.section_key === "matching-algorithms"
  const isSubscriptionSelectorSection = currentSection.content === "interactive-subscription-selector"
  const isBusinessCaseSection = currentSection.section_key === "business-case"

  console.log(
    "[v0] Current section:",
    currentSection.title,
    "section_key:",
    currentSection.section_key,
    "id:",
    currentSection.id,
  )
  console.log("[v0] isBusinessCaseSection:", isBusinessCaseSection)

  const currentIndex = sections.findIndex((section) => section.id === activeSection)
  const nextSection = currentIndex < sections.length - 1 ? sections[currentIndex + 1].id : null

  const macroSectionId = getMacroSectionForSection(activeSection)
  const macroSection = macroSections.find((m) => m.id === macroSectionId)
  const macroSectionTitle = macroSection?.title || currentSection.phase

  const handlePreviousSection = () => {
    const currentIndex = sections.findIndex((section) => section.id === activeSection)
    const previousSectionId = currentIndex > 0 ? sections[currentIndex - 1].id : null

    if (previousSectionId) {
      const currentMacroSection = getMacroSectionForSection(activeSection)
      const previousMacroSection = getMacroSectionForSection(previousSectionId)

      analytics.trackSectionNavigation(
        activeSection,
        previousSectionId,
        "previous",
        currentMacroSection,
        previousMacroSection,
      )
      onSectionChange(previousSectionId)
    }
  }

  const handleNextSection = () => {
    if (nextSection) {
      const currentMacroSection = getMacroSectionForSection(activeSection)
      const nextMacroSection = getMacroSectionForSection(nextSection)

      analytics.trackSectionNavigation(activeSection, nextSection, "next", currentMacroSection, nextMacroSection)
      onSectionChange(nextSection)
    }
  }

  const handleProposalClick = () => {
    analytics.trackProposalCTA(currentSection.title.toLowerCase(), "click")
    if (content.proposalUrl) {
      window.open(content.proposalUrl, "_blank")
    }
  }

  if (isSubscriptionSelectorSection) {
    return (
      <div className="max-w-7xl mx-auto px-6 py-8 space-y-10">
        <div className="flex items-center gap-3 mb-2">
          <Badge
            variant="secondary"
            className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 text-xs font-medium border-0"
          >
            {macroSectionTitle}
          </Badge>
          <span className="text-xs text-gray-400 dark:text-gray-500 font-medium">
            Section {currentIndex + 1} of {sections.length}
          </span>
        </div>

        {/* Section Title */}
        <div className="space-y-3">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight text-balance">
            {currentSection.title}
          </h1>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 max-w-2xl mx-auto">
          <div className="flex items-center justify-center space-x-2">
            <Info className="h-4 w-4 text-blue-600" />
            <p className="text-blue-800 font-medium text-sm">
              Choose a plan to instantly view recalculated timelines, costs, and outcomes for each phase.
            </p>
          </div>
        </div>

        {/* Interactive Subscription Tier Selector */}
        <div className="bg-white dark:bg-gray-900 rounded-2xl p-10 border border-gray-200 dark:border-gray-800 shadow-sm">
          <SubscriptionTierSelector />
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between pt-8 border-t border-gray-200 dark:border-gray-800">
          <Button
            variant="outline"
            onClick={handlePreviousSection}
            disabled={currentIndex <= 0}
            className="border-gray-300 dark:border-gray-600 px-6 py-3 font-medium bg-transparent"
          >
            Previous Section
          </Button>

          {nextSection && (
            <Button
              onClick={handleNextSection}
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Next: {sections.find((s) => s.id === nextSection)?.title || "Next Section"}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <ProposalCTA sectionTitle={currentSection.title.toLowerCase()} onProposalClick={handleProposalClick} />
      </div>
    )
  }

  if (isMatchingSection) {
    return (
      <div className="max-w-6xl mx-auto px-6 py-8 space-y-10">
        <div className="flex items-center gap-3 mb-2">
          <Badge
            variant="secondary"
            className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 text-xs font-medium border-0"
          >
            {macroSectionTitle}
          </Badge>
          <span className="text-xs text-gray-400 dark:text-gray-500 font-medium">
            Section {currentIndex + 1} of {sections.length}
          </span>
        </div>

        {/* Section Title */}
        <div className="space-y-3">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight text-balance">
            {currentSection.title}
          </h1>
        </div>

        {/* Interactive Matching Interface */}
        <MatchingInterface />

        {/* Navigation */}
        <div className="flex items-center justify-between pt-8 border-t border-gray-200 dark:border-gray-800">
          <Button
            variant="outline"
            onClick={handlePreviousSection}
            disabled={currentIndex <= 0}
            className="border-gray-300 dark:border-gray-600 px-6 py-3 font-medium bg-transparent"
          >
            Previous Section
          </Button>

          {nextSection && (
            <Button
              onClick={handleNextSection}
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Next: {sections.find((s) => s.id === nextSection)?.title || "Next Section"}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <ProposalCTA sectionTitle={currentSection.title.toLowerCase()} onProposalClick={handleProposalClick} />
      </div>
    )
  }

  if (isBusinessCaseSection) {
    const businessContent = currentSection.content as any

    const renderTextBlock = (block: { title: string; body: string; bullets?: string[] }) => (
      <div>
        <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">{block.title}</h3>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6 text-lg">{block.body}</p>
        {block.bullets && (
          <ul className="list-none space-y-4 my-6">
            {block.bullets.map((item, index) => (
              <li key={index} className="flex items-start gap-4">
                <div className="w-2 h-2 bg-orange-600 rounded-full mt-3 flex-shrink-0"></div>
                <span className="text-gray-700 dark:text-gray-300 leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    )

    const objectiveStyles: Record<string, { container: string; iconBg: string; heading: string; text: string; dot: string }> = {
      blue: {
        container:
          "bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20 border border-blue-200 dark:border-blue-800 shadow-lg",
        iconBg: "bg-blue-100 dark:bg-blue-900/30",
        heading: "text-blue-900 dark:text-blue-100",
        text: "text-blue-800 dark:text-blue-200",
        dot: "bg-blue-600",
      },
      orange: {
        container:
          "bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 border border-orange-200 dark:border-orange-800 shadow-lg",
        iconBg: "bg-orange-100 dark:bg-orange-900/30",
        heading: "text-orange-900 dark:text-orange-100",
        text: "text-orange-800 dark:text-orange-200",
        dot: "bg-orange-600",
      },
      purple: {
        container:
          "bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 border border-purple-200 dark:border-purple-800 shadow-lg",
        iconBg: "bg-purple-100 dark:bg-purple-900/30",
        heading: "text-purple-900 dark:text-purple-100",
        text: "text-purple-800 dark:text-purple-200",
        dot: "bg-purple-600",
      },
    }

    return (
      <div className="max-w-4xl mx-auto px-6 py-8 space-y-10">
        <div className="flex items-center gap-3 mb-2">
          <Badge
            variant="secondary"
            className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 text-xs font-medium border-0"
          >
            {macroSectionTitle}
          </Badge>
          <span className="text-xs text-gray-400 dark:text-gray-500 font-medium">
            Section {currentIndex + 1} of {sections.length}
          </span>
        </div>

        {/* Section Title */}
        <div className="space-y-3">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight text-balance">
            {currentSection.title}
          </h1>
        </div>

        {/* Market Opportunity and Current Constraints */}
        <div className="bg-white dark:bg-gray-900 rounded-2xl p-10 border border-gray-200 dark:border-gray-800 shadow-sm">
          <div className="space-y-8">
            {businessContent.marketOpportunity && renderTextBlock(businessContent.marketOpportunity)}
            {businessContent.constraints && renderTextBlock(businessContent.constraints)}
          </div>
        </div>

        {(businessContent.objectives?.length || businessContent.provenTrackRecord) && (
          <div className="space-y-6">
            {businessContent.objectives?.map((obj: any, index: number) => {
              const styles = objectiveStyles[obj.color as keyof typeof objectiveStyles] || objectiveStyles.blue
              return (
                <div key={index} className={`${styles.container} rounded-2xl p-8`}>
                  <div className="flex items-center gap-3 mb-4">
                    <div className={`${styles.iconBg} p-2 rounded-lg`}>
                      <span className="text-2xl">{obj.icon}</span>
                    </div>
                    <h3 className={`text-xl font-bold ${styles.heading}`}>{obj.title}</h3>
                  </div>
                  <p className={`${styles.text} leading-relaxed font-medium mb-4`}>{obj.description}</p>
                  <div className="space-y-3">
                    {obj.keyResults.map((kr: string, krIndex: number) => (
                      <div key={krIndex} className="flex items-start gap-3">
                        <div className={`w-2 h-2 ${styles.dot} rounded-full mt-2 flex-shrink-0`}></div>
                        <p className={`${styles.text} font-medium text-sm`}>{kr}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
            {businessContent.provenTrackRecord && (
              <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-2xl p-8 border border-green-200 dark:border-green-800 shadow-lg">
                <div className="flex items-center gap-3 mb-4">
                  <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded-lg">
                    <span className="text-2xl">✅</span>
                  </div>
                  <h3 className="text-xl font-bold text-green-900 dark:text-green-100">
                    {businessContent.provenTrackRecord.title}
                  </h3>
                </div>
                <p className="text-green-800 dark:text-green-200 leading-relaxed font-medium">
                  {businessContent.provenTrackRecord.body}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="flex items-center justify-between pt-8 border-t border-gray-200 dark:border-gray-800">
          <Button
            variant="outline"
            onClick={handlePreviousSection}
            disabled={currentIndex <= 0}
            className="border-gray-300 dark:border-gray-600 px-6 py-3 font-medium bg-transparent"
          >
            Previous Section
          </Button>

          {nextSection && (
            <Button
              onClick={handleNextSection}
              className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
            >
              Next: {sections.find((s) => s.id === nextSection)?.title || "Next Section"}
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </div>

        <ProposalCTA sectionTitle={currentSection.title.toLowerCase()} onProposalClick={handleProposalClick} />
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto px-6 py-8 space-y-10">
      <div className="flex items-center gap-3 mb-2">
        <Badge
          variant="secondary"
          className="bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 px-3 py-1 text-xs font-medium border-0"
        >
          {macroSectionTitle}
        </Badge>
        <span className="text-xs text-gray-400 dark:text-gray-500 font-medium">
          Section {currentIndex + 1} of {sections.length}
        </span>
      </div>

      {/* Section Title */}
      <div className="space-y-3">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white leading-tight text-balance">
          {currentSection.title}
        </h1>
      </div>

      {/* Section Content */}
      <div className="bg-white dark:bg-gray-900 rounded-2xl p-10 border border-gray-200 dark:border-gray-800 shadow-sm">
        <div className="prose prose-gray dark:prose-invert max-w-none prose-lg">
          {(() => {
            let paragraphs: string[] = []

            if (typeof currentSection.content === "string") {
              paragraphs = currentSection.content.split("\n\n")
            } else if (Array.isArray(currentSection.content)) {
              paragraphs = currentSection.content
            } else {
              return (
                <p className="text-gray-600 dark:text-gray-400">No textual content</p>
              )
            }

            return paragraphs.map((paragraph, index) => {
              if (paragraph.startsWith("**") && paragraph.endsWith("**")) {
                return (
                  <h3
                    key={index}
                    className="text-2xl font-bold text-gray-900 dark:text-white mt-8 mb-6 first:mt-0"
                  >
                    {paragraph.replace(/\*\*/g, "")}
                  </h3>
                )
              }
              if (paragraph.startsWith("*") && paragraph.endsWith("*")) {
                return (
                  <h4
                    key={index}
                    className="text-xl font-semibold text-gray-800 dark:text-gray-200 mt-6 mb-4"
                  >
                    {paragraph.replace(/\*/g, "")}
                  </h4>
                )
              }
              if (paragraph.startsWith("•")) {
                const items = paragraph
                  .split("\n")
                  .filter((item) => item.startsWith("•"))
                return (
                  <ul key={index} className="list-none space-y-4 my-6">
                    {items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-4">
                        <div className="w-2 h-2 bg-orange-600 rounded-full mt-3 flex-shrink-0"></div>
                        <span className="text-gray-700 dark:text-gray-300 leading-relaxed">
                          {item.replace("• ", "")}
                        </span>
                      </li>
                    ))}
                  </ul>
                )
              }
              return (
                <p
                  key={index}
                  className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6 text-lg"
                >
                  {paragraph}
                </p>
              )
            })
          })()}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex items-center justify-between pt-8 border-t border-gray-200 dark:border-gray-800">
        <Button
          variant="outline"
          onClick={handlePreviousSection}
          disabled={currentIndex <= 0}
          className="border-gray-300 dark:border-gray-600 px-6 py-3 font-medium bg-transparent"
        >
          Previous Section
        </Button>

        {nextSection && (
          <Button
            onClick={handleNextSection}
            className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 font-medium shadow-lg hover:shadow-xl transition-all duration-200"
          >
            Next: {sections.find((s) => s.id === nextSection)?.title || "Next Section"}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>

      <ProposalCTA sectionTitle={currentSection.title.toLowerCase()} onProposalClick={handleProposalClick} />
    </div>
  )
}

export { PepContent }
export default PepContent
