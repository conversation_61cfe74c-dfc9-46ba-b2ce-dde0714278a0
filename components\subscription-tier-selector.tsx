"use client"

import { useState } from "react"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, DollarSign, Zap, Info } from "lucide-react"
import { Phase, phases } from "@/lib/platform-config"

interface SubscriptionTier {
  id: string
  name: string
  price: number
  velocity: number
  description: string
  features: string[]
  teamType: string
  teamDescription: string
  objectives: number
  keyResults: number
}


const tiers: SubscriptionTier[] = [
  {
    id: "validate",
    name: "Validate",
    price: 4750,
    velocity: 50,
    description: "Standard priority with validation focus",
    features: ["Product Strategist", "Senior Developer"],
    teamType: "Your Fractional Team",
    teamDescription: "Expert resources allocated across select projects, scaling with your needs.",
    objectives: 1,
    keyResults: 2,
  },
  {
    id: "launch",
    name: "Launch",
    price: 7500,
    velocity: 80,
    description: "Enhanced priority with go-to-market focus",
    features: ["Product Architect", "Technical Lead"],
    teamType: "Your Enhanced Fractional Team",
    teamDescription: "Expert resources allocated across select projects, scaling with your needs.",
    objectives: 2,
    keyResults: 5,
  },
  {
    id: "scale",
    name: "Scale",
    price: 10500,
    velocity: 125,
    description: "Premium priority with scaling focus",
    features: ["Innovation Architect"],
    teamType: "Your Embedded Fractional Team",
    teamDescription:
      "Strategic resources deeply integrated into your project with high availability and leadership focus — without full exclusivity.",
    objectives: 3,
    keyResults: 9,
  },
]


export function SubscriptionTierSelector() {
  const [selectedTier, setSelectedTier] = useState<string>("launch")
  const [highlightedPhase, setHighlightedPhase] = useState<string | null>(null)

  const formatInvestmentRange = (min: number, max: number) => {
    const formatValue = (value: number) => {
      if (value >= 10000) {
        return `$${Math.round(value / 1000)}K`
      }
      return `$${value.toLocaleString()}`
    }
    return `${formatValue(min)}–${formatValue(max)}`
  }

  const calculatePhaseRanges = (phase: Phase, tier: SubscriptionTier) => {
    const sprintsMin = Math.ceil(phase.storyPointsMin / tier.velocity)
    const sprintsMax = Math.ceil(phase.storyPointsMax / tier.velocity)
    const weeksMin = sprintsMin * 2
    const weeksMax = sprintsMax * 2
    const spRate = tier.price / tier.velocity
    const investmentMin = Math.round((phase.storyPointsMin * spRate) / 100) * 100
    const investmentMax = Math.round((phase.storyPointsMax * spRate) / 100) * 100

    return {
      sprintsMin,
      sprintsMax,
      weeksMin,
      weeksMax,
      investmentMin,
      investmentMax,
      spRate,
    }
  }

  const selectedTierData = tiers.find((t) => t.id === selectedTier)!
  const validateTier = tiers.find((t) => t.id === "validate")!

  const validatePhaseRanges = phases.map((phase) => calculatePhaseRanges(phase, validateTier))
  const validatePhaseAverages = validatePhaseRanges.map((detail) => ({
    ...detail,
    avgWeeks: Math.round((detail.weeksMin + detail.weeksMax) / 2),
  }))
  const baselineTotalWeeks = validatePhaseAverages.reduce((sum, detail) => sum + detail.avgWeeks, 0)

  const phaseRanges = phases.map((phase) => calculatePhaseRanges(phase, selectedTierData))
  const phaseAverages = phaseRanges.map((detail) => ({
    ...detail,
    avgWeeks: Math.round((detail.weeksMin + detail.weeksMax) / 2),
  }))
  const totalAvgWeeks = phaseAverages.reduce((sum, detail) => sum + detail.avgWeeks, 0)

  const totalWeeksMin = phaseRanges.reduce((sum, detail) => sum + detail.weeksMin, 0)
  const totalWeeksMax = phaseRanges.reduce((sum, detail) => sum + detail.weeksMax, 0)
  const totalInvestmentMin = phaseRanges.reduce((sum, detail) => sum + detail.investmentMin, 0)
  const totalInvestmentMax = phaseRanges.reduce((sum, detail) => sum + detail.investmentMax, 0)

  const scrollToPhase = (phaseId: string) => {
    const element = document.getElementById(`phase-${phaseId}`)
    if (element) {
      element.scrollIntoView({ behavior: "smooth", block: "center" })
    }
  }

  return (
    <div className="space-y-8">
      <div className="text-center space-y-6">
        <div className="space-y-2">
          <h3 className="text-2xl font-bold text-gray-900">Subscription Plans for Ongoing Development</h3>
        </div>

        <div className="grid md:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {tiers.map((tier) => (
            <Card
              key={tier.id}
              className={`cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${
                selectedTier === tier.id
                  ? "ring-2 ring-orange-500 shadow-xl -translate-y-1 bg-white"
                  : "hover:ring-1 hover:ring-gray-300 bg-white"
              }`}
              onClick={() => setSelectedTier(tier.id)}
            >
              <CardContent className="p-5 space-y-4">
                <div className="text-center space-y-2">
                  <h4 className="text-lg font-bold uppercase tracking-wide text-gray-900 letter-spacing-wider">
                    {tier.name}
                  </h4>

                  <div className="space-y-1">
                    <div className="text-3xl font-bold text-orange-600">${tier.price.toLocaleString()}</div>
                    <div className="text-xs font-medium text-gray-500">/bi-weekly</div>
                  </div>
                </div>

                <div className="text-center space-y-2">
                  <p className="text-xs text-gray-600 font-medium leading-tight">{tier.description}</p>

                  <div className="flex items-center justify-center space-x-2 text-cyan-600">
                    <Zap className="h-3 w-3" />
                    <span className="text-sm font-bold">{tier.velocity} SP</span>
                    <span className="text-xs text-gray-500">/bi-weekly</span>
                  </div>
                </div>

                <div className="px-2">
                  <div className="h-1.5 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full transition-all duration-500"
                      style={{ width: `${(tier.velocity / 125) * 100}%` }}
                    ></div>
                  </div>
                </div>

                {selectedTier === tier.id && (
                  <div className="flex items-center justify-center space-x-2 text-orange-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-semibold">Selected Plan</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="space-y-8">
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-bold text-gray-900">Project Timeline Overview</h3>

          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center space-x-2 bg-cyan-500 text-white px-4 py-2 rounded-full shadow-lg">
              <Clock className="h-4 w-4" />
              <span className="font-bold text-sm">{totalAvgWeeks} weeks</span>
            </div>
            <div className="flex items-center space-x-2 bg-orange-500 text-white px-4 py-2 rounded-full shadow-lg">
              <DollarSign className="h-4 w-4" />
              <span className="font-bold text-sm">{formatInvestmentRange(totalInvestmentMin, totalInvestmentMax)}</span>
            </div>
          </div>

          <p className="text-gray-600">
            Total project scope with <span className="font-semibold text-blue-600">{selectedTierData.name}</span> tier
          </p>
        </div>

        <div className="max-w-4xl mx-auto space-y-6">
          <div className="relative bg-gray-200 rounded-full h-10 overflow-hidden shadow-inner">
            {phaseAverages.map((detail, index) => {
              const widthPercentage = (detail.avgWeeks / baselineTotalWeeks) * 100
              const leftOffset = phaseAverages.slice(0, index).reduce((sum, prevDetail) => {
                return sum + (prevDetail.avgWeeks / baselineTotalWeeks) * 100
              }, 0)

              return (
                <div
                  key={phases[index].id}
                  className={`absolute top-0 h-full cursor-pointer transition-all duration-500 ease-in-out transform hover:scale-y-110 ${
                    highlightedPhase === phases[index].id
                      ? "bg-gradient-to-r from-cyan-500 to-cyan-600 shadow-lg z-10"
                      : "bg-gradient-to-r from-cyan-400 to-cyan-500"
                  }`}
                  style={{
                    left: `${leftOffset}%`,
                    width: `${widthPercentage}%`,
                  }}
                  onMouseEnter={() => setHighlightedPhase(phases[index].id)}
                  onMouseLeave={() => setHighlightedPhase(null)}
                  onClick={() => scrollToPhase(phases[index].id)}
                  title={`${phases[index].name} - ${detail.avgWeeks} weeks average`}
                >
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-xs font-bold text-white drop-shadow-sm px-1 truncate">Phase {index + 1}</span>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-50"></div>
                  {index < phases.length - 1 && (
                    <div className="absolute right-0 top-0 w-1 h-full bg-orange-400 shadow-sm"></div>
                  )}
                </div>
              )
            })}
          </div>

          <div className="space-y-4">
            <div className="relative min-h-[2rem]">
              {phaseAverages.map((detail, index) => {
                const widthPercentage = (detail.avgWeeks / baselineTotalWeeks) * 100
                const leftOffset = phaseAverages.slice(0, index).reduce((sum, prevDetail) => {
                  return sum + (prevDetail.avgWeeks / baselineTotalWeeks) * 100
                }, 0)

                const compactLabels = ["Full-Stack Migration", "Foundation & Multi-Tenant", "Commercial", "Evolution"]

                return (
                  <div
                    key={phases[index].id}
                    className={`absolute flex flex-col items-center transition-all duration-500 cursor-pointer ${
                      highlightedPhase === phases[index].id ? "transform scale-110" : ""
                    }`}
                    style={{
                      left: `${leftOffset + widthPercentage / 2}%`,
                      transform: `translateX(-50%) ${highlightedPhase === phases[index].id ? "scale(1.1)" : ""}`,
                    }}
                    onMouseEnter={() => setHighlightedPhase(phases[index].id)}
                    onMouseLeave={() => setHighlightedPhase(null)}
                    onClick={() => scrollToPhase(phases[index].id)}
                  >
                    <div
                      className={`text-lg font-bold transition-colors duration-300 ${
                        highlightedPhase === phases[index].id ? "text-cyan-700" : "text-gray-900"
                      }`}
                    >
                      {detail.avgWeeks}w
                    </div>
                    <div
                      className={`text-xs max-w-20 leading-tight text-center transition-colors duration-300 truncate ${
                        highlightedPhase === phases[index].id ? "text-cyan-600 font-medium" : "text-gray-500"
                      }`}
                      title={phases[index].name}
                    >
                      {compactLabels[index]}
                    </div>
                  </div>
                )
              })}
            </div>

            <div className="flex justify-between items-center text-sm pt-2 border-t border-gray-100">
              <div className="w-full text-center">
                <p className="text-gray-500 text-xs font-normal">
                  Durations are estimates; click a phase for details and ranges.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-6 pt-12">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">Development Phases</h3>
          <p className="text-gray-600">
            Timeline and investment calculated for{" "}
            <span className="font-semibold text-blue-600">{selectedTierData.name}</span> tier (
            {selectedTierData.velocity} SP/bi-weekly)
          </p>
          <div className="mt-4 flex items-center justify-center space-x-2 text-sm text-gray-500">
            <Info className="h-4 w-4" />
            <span>Estimates are agile-based and presented as ranges to reflect prioritization flexibility.</span>
          </div>
        </div>

        <div className="relative">
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-cyan-100 via-blue-100 to-green-100 opacity-60 hidden lg:block"></div>

          <div className="space-y-6">
            {phases.map((phase, index) => {
              const ranges = calculatePhaseRanges(phase, selectedTierData)
              const PhaseIcon = phase.icon

              return (
                <div key={phase.id} className="relative" id={`phase-${phase.id}`}>
                  <div className="absolute -left-2 top-4 w-16 h-8 bg-gradient-to-r from-cyan-400 to-blue-400 rounded-full flex items-center justify-center shadow-md z-10 hidden lg:flex">
                    <PhaseIcon className="h-4 w-4 text-white" />
                  </div>

                  {index < phases.length - 1 && (
                    <div className="absolute left-8 -bottom-3 w-4 h-4 bg-white border-2 border-gray-300 rounded-full flex items-center justify-center z-20 hidden lg:flex">
                      <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    </div>
                  )}

                  <Card className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 ml-0 lg:ml-12">
                    <CardContent className="p-0">
                      <div className="grid lg:grid-cols-2 gap-0">
                        <div className="p-4 bg-gray-50 border-r border-gray-200">
                          <div className="space-y-3">
                            <Badge className="lg:hidden bg-gradient-to-r from-cyan-500 to-blue-500 text-white">
                              <PhaseIcon className="h-3 w-3 mr-1" />
                              Phase {index + 1}
                            </Badge>

                            <div>
                              <h4 className="text-xl font-bold text-gray-900 leading-tight mb-1">{phase.name}</h4>
                              <p className="text-sm text-gray-500 font-medium mb-2">{phase.subtitle}</p>
                              <Badge variant="outline" className="text-cyan-600 border-cyan-300 bg-cyan-50">
                                ~{phase.storyPointsMin}–{phase.storyPointsMax} SP
                              </Badge>
                            </div>

                            <div className="space-y-2">
                              <h5 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Scope</h5>
                              <div className="space-y-1">
                                {phase.scope.map((item, idx) => (
                                  <div key={idx} className="flex items-start space-x-2">
                                    <CheckCircle className="h-3.5 w-3.5 text-cyan-500 mt-0.5 flex-shrink-0" />
                                    <span className="text-sm text-gray-700 leading-tight">
                                      {item.includes(":") ? (
                                        <>
                                          <span className="font-bold">{item.split(":")[0]}:</span>
                                          <span>{item.split(":").slice(1).join(":")}</span>
                                        </>
                                      ) : (
                                        item
                                      )}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="p-4 space-y-3">
                          <div className="grid gap-3">
                            <div className="bg-cyan-50 border border-cyan-200 rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-1">
                                <Clock className="h-4 w-4 text-cyan-600" />
                                <h5 className="font-semibold text-cyan-800 text-sm">Timeline</h5>
                              </div>
                              <div className="flex items-baseline space-x-2">
                                <span className="text-xl font-bold text-cyan-700">
                                  {ranges.weeksMin === ranges.weeksMax
                                    ? `~${ranges.weeksMin}`
                                    : `~${ranges.weeksMin}–${ranges.weeksMax}`}
                                </span>
                                <span className="text-xs text-cyan-600">weeks</span>
                              </div>
                              <div className="mt-2 h-1.5 bg-cyan-200 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-full"
                                  style={{ width: `${(ranges.weeksMin / ranges.weeksMax) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="bg-orange-25 border border-orange-200 rounded-lg p-3">
                              <div className="flex items-center space-x-2 mb-1">
                                <DollarSign className="h-4 w-4 text-orange-500" />
                                <h5 className="font-semibold text-orange-700 text-sm">Investment</h5>
                              </div>
                              <div className="flex items-baseline space-x-2">
                                <span className="text-xl font-bold text-orange-600">
                                  {formatInvestmentRange(ranges.investmentMin, ranges.investmentMax)}
                                </span>
                              </div>
                              <div className="mt-2 h-1.5 bg-orange-200 rounded-full overflow-hidden">
                                <div
                                  className="h-full bg-gradient-to-r from-orange-400 to-orange-600 rounded-full"
                                  style={{ width: `${(ranges.investmentMin / ranges.investmentMax) * 100}%` }}
                                ></div>
                              </div>
                            </div>

                            <div className="bg-green-50 border-2 border-green-300 rounded-lg p-3 shadow-sm">
                              <div className="flex items-center space-x-2 mb-2">
                                <div className="flex items-center justify-center w-5 h-5 bg-green-500 rounded-full">
                                  <CheckCircle className="h-3 w-3 text-white" />
                                </div>
                                <h5 className="font-bold text-green-800 text-sm">Outcome:</h5>
                              </div>
                              <p className="text-sm font-semibold text-green-800 leading-tight">{phase.outcome}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              )
            })}
          </div>
        </div>

        <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <h4 className="text-xl font-bold text-gray-900">Total Project Investment</h4>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-blue-600">
                    ~{totalWeeksMin} - {totalWeeksMax} weeks
                  </div>
                  <div className="text-sm text-gray-600">Total Timeline</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatInvestmentRange(totalInvestmentMin, totalInvestmentMax)}
                  </div>
                  <div className="text-sm text-gray-600">Total Investment</div>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold text-green-600">
                    ~{phases.reduce((total, phase) => total + phase.storyPointsMin, 0)}–
                    {phases.reduce((total, phase) => total + phase.storyPointsMax, 0)} SP
                  </div>
                  <div className="text-sm text-gray-600">Total Story Points</div>
                </div>
              </div>
              <p className="text-sm text-gray-600 max-w-2xl mx-auto">
                Estimates presented as directional investment envelopes to reflect agile prioritization and scope
                flexibility. Higher velocity tiers maintain lower cost per story point.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SubscriptionTierSelector
