import { Rocket, Building, Handshake, Shield, type LucideIcon } from "lucide-react"

export const metadata = {
  id: "tugboat-ima",
  title: "Tugboat Inventory Mini-App (IMA)",
  description:
    "Scalable mobile-first inventory management platform for property owners to document assets through AI-powered photo processing and deduplication",
  clientName: "Tugboat Team",
}

export const platformConfig = {
  metadata,
  branding: {
    logo: "/images/singular-logo.png",
    accentColor: "#f59e0b",
    companyName: "Product Execution Proposal",
    companyDescription: "Singular Agency",
  },
  variables: {
    projectName: "Tugboat Inventory Mini-App (IMA)",
    timeline: "4", // POC timeline in weeks
    budget: "$45,000", // POC budget estimate
    expectedROI: "3x",
    marketSize: "$2.1B", // Property management software market
    developmentWeeks: "4", // POC development timeline
  },
  content: {
    proposalUrl: "https://singular.proposify.com/preview/RWt5Nk1rc05BL2NvYVdiUjNSTW1xZz09",
    hero: {
      title: "Tugboat: Transform Property Inventory with AI Automation",
      subtitle: "",
      description:
        "A mobile-first platform that streamlines property documentation through AI-driven photo recognition, smart deduplication, and bulk processing — accelerating insurance claims and asset management.",
      primaryCTA: "Explore Our Methodology",
      secondaryCTA: "View Project Summary",
      features: ["AI Object Detection", "Smart Deduplication", "Mobile-First Design", "Bulk Processing"],
    },
    statistics: [
      {
        id: 1,
        value: "800+",
        label: "Current Users",
        icon: { name: "Users" },
        color: "#3b82f6",
      },
      {
        id: 2,
        value: "7,000+",
        label: "Items Processed",
        icon: { name: "Camera" },
        color: "#f59e0b",
      },
      {
        id: 3,
        value: "<5min",
        label: "Processing Time",
        icon: { name: "Clock" },
        color: "#10b981",
      },
      {
        id: 4,
        value: "1M+",
        label: "Records Supported",
        icon: { name: "TrendingUp" },
        color: "#8b5cf6",
      },
    ],
    methodology: {
      title: "Our Proven Development Methodology",
      description:
        "Transform your vision into a world-class platform with our proven 5-phase methodology. From design thinking to long-term evolution, we deliver exceptional results that drive business growth.",
    },
    cta: {
      title: "Ready to Transform Your Vision?",
      description:
        "Partner with Singular Agency to build exceptional digital experiences that drive growth and exceed user expectations.",
      buttonText: "Get Your Proposal",
    },
  },
  macroSections: [
    {
      id: "overview",
      title: "Project Overview",
      icon: {
        name: "FileText",
      },
      sections: [
        {
          id: 1,
          section_key: "executive-summary",
          title: "Executive Summary",
          icon: {
            name: "FileText",
          },
          phase: "Design Thinking",
          content:
            "**Project Vision**\n\nThe {{projectName}} represents a revolutionary approach to property inventory management, transforming how property owners document and manage their assets through AI-powered photo processing and intelligent deduplication. This platform serves as a comprehensive ecosystem where users can efficiently capture, categorize, and export detailed property inventories for insurance claims and asset management.\n\n**Key Innovation Areas**\n\n• AI-powered object detection and metadata extraction using OpenAI Vision APIs\n• Advanced image deduplication using imagededup algorithms\n• Mobile-first Flutter application with polished user experience\n• Scalable backend architecture with Vercel and custom microservices\n• Bulk photo processing with concurrent AI workflows\n• White-label onboarding for commercial clients\n\n**Strategic Approach**\n\nSingular Agency's proven methodology ensures systematic development from proof-of-concept to production-ready platform. Our approach emphasizes mobile-first design, AI-driven automation, and scalable architecture that can handle millions of records while maintaining performance and user experience excellence.",
        },
        {
          id: 2,
          section_key: "business-case",
          title: "Business Case & ROI Model",
          icon: {
            name: "TrendingUp",
          },
          phase: "Design Thinking",
          content: {
            marketOpportunity: {
              title: "Market Opportunity",
              body:
                "The property inventory management market is expanding rapidly, with demand rising from both residential households and enterprise/HOA operators. Early traction through the proof-of-concept (~800 members and >7,000 items processed) confirms strong product–market fit. With scalability and automation, the platform can address two converging opportunities:",
              bullets: [
                "B2C residential subscriptions for individual property owners.",
                "Enterprise/HOA contracts where multi-property support, compliance, and analytics drive higher-value engagements.",
                "White-label and service-provider SaaS opportunities, enabling partners to onboard their own customer bases under branded portals.",
              ],
            },
            constraints: {
              title: "Current Constraints & Need for Investment",
              body:
                "The current proof-of-concept operates within technical constraints that limit scalability—restricted data handling and slow batch processing. These bottlenecks confirm both the urgency and the opportunity for investment. Phase 1 directly resolves these issues by migrating to a scalable full-stack architecture, unlocking faster performance and laying the foundation for sustainable growth.",
            },
            objectives: [
              {
                icon: "🎯",
                color: "blue",
                title: "Objective 1",
                description:
                  "Deliver a market-ready, mobile-first inventory app that can scale beyond no-code limitations.",
                keyResults: [
                  "KR1: Launch a fully functional mobile + web version with p95 ≤ 5 min for 100-photo uploads.",
                  "KR2: Achieve ≤ $0.02 all-in cost per analyzed photo at ≥100k photos/month.",
                  "KR3: Eliminate current Airtable/Make bottlenecks (timeouts, row caps, lack of concurrency).",
                ],
              },
              {
                icon: "🚀",
                color: "orange",
                title: "Objective 2",
                description:
                  "Enhance user experience to drive adoption and retention.",
                keyResults: [
                  "KR1: Mobile app supports 100% of photo ingestion workflows (no upload failures on Safari/PWA).",
                  "KR2: Increase average items uploaded per session by +25% compared to the no-code baseline.",
                  "KR3: Introduce inline editing + dedupe UX that reduces user cleanup time by ≥40%.",
                ],
              },
              {
                icon: "✏️",
                color: "purple",
                title: "Objective 3",
                description:
                  "Unlock growth from new market segments (inventory companies, HOAs, high net worth).",
                keyResults: [
                  "KR1: Enable multi-property management (10+ properties per account) without performance degradation.",
                  "KR2: Support bulk photo processing at ≥100k items across 50+ properties in test scenarios.",
                ],
              },
            ],
            provenTrackRecord: {
              title: "Proven Track Record",
              body:
                "We've successfully guided other clients through similar inflection points where scale and cost discipline become critical. Our approach balances speed to market with architectural decisions that don't create technical debt—ensuring your investment in Phase 1 becomes the foundation for sustainable growth.",
            },
          },
        },
        {
          id: 3,
          section_key: "product-vision",
          title: "Product Vision & Goals",
          icon: {
            name: "Target",
          },
          phase: "Design Thinking",
          content:
            '**Core Platform Vision**\n\nBuild a next-generation, mobile-first inventory management platform that leverages AI to transform property documentation. The goal is to empower property owners, HOAs, and service providers to efficiently capture, process, and manage assets for insurance, compliance, and portfolio management—turning today\'s manual, error-prone processes into a seamless digital experience.\n\n**Strategic Pillars**\n\nMobile-First Experience → A polished, intuitive mobile app designed for on-the-go documentation, with offline-first capabilities.\n\nAI-Powered Automation → Intelligent photo recognition, metadata extraction, and deduplication that reduce manual effort and speed up claims and audits.\n\nEnterprise-Grade Scalability → Architecture built to handle millions of records with sub-2-second response times, ensuring confidence at both B2C and enterprise scale.\n\nMulti-Tenant White-Labeling → Onboarding for HOAs, commercial clients, and partners with branded environments that open additional revenue streams.\n\nFlexible Data Outputs → Exportable CSV, XLS, and PDF reports that align with insurer, compliance, and enterprise requirements.\n\nModern Data Organization → Transition from static, geospatial "areas" to dynamic collections, allowing greater flexibility in categorization and reporting.\n\n**User Experience Philosophy**\n\nInventory management is often tedious; our platform flips that script. The experience emphasizes simplicity, speed, and engagement:\n\nInline editing and dynamic dashboards keep workflows frictionless.\n\nGamified progress indicators motivate users to complete inventories.\n\nThe result is a platform that feels approachable, not burdensome—driving adoption and retention.\n\n**Technology Integration Strategy**\n\nThe technical foundation is a means to business outcomes:\n\nFlutter ensures consistent, high-quality mobile performance across devices.\n\nVercel delivers scalable deployment with low latency, keeping operations smooth as adoption grows.\n\nCustom microservices handle AI-driven image processing at scale, eliminating the bottlenecks of the no-code PoC and enabling experiences that directly differentiate the product in the market.',
        },
        {
          id: 4,
          section_key: "phases-investment",
          title: "Phases & Investment",
          icon: {
            name: "Layers",
          },
          phase: "Design Thinking",
          content: "interactive-subscription-selector", // Changed to use interactive component
        },
      ],
    },
    {
      id: "requirements",
      title: "Requirements & Design",
      icon: {
        name: "CheckSquare",
      },
      sections: [
        {
          id: 5,
          section_key: "functional-requirements",
          title: "Functional Requirements",
          icon: {
            name: "CheckSquare",
          },
          phase: "Prototype Phase",
          content:
            "**Core Inventory Management**\n\n• Property creation and management with address, nickname, and metadata\n• Collection-based organization (replacing geospatial 'areas' concept)\n• Bulk photo and video uploads supporting 100+ files per batch\n• AI-powered object detection with title, description, brand, model, condition, quantity, and estimated value\n• Inline editing of all item attributes without modal interactions\n• Export functionality in CSV, XLS, and PDF formats\n\n**AI-Powered Processing**\n\n• OpenAI Vision API integration for object recognition and metadata extraction\n• Automated intra-batch deduplication using imagededup algorithms\n• Concurrent photo processing to reduce batch time from 20+ minutes to <5 minutes\n• User-facing duplicate review and cleanup interface\n• Intelligent matching based on visual similarity and metadata\n\n**Mobile-First Experience**\n\n• Flutter-based mobile application with native performance\n• Offline photo capture and queued upload capabilities\n• Responsive design optimized for mobile inventory management\n• Touch-friendly interfaces with swipe gestures and intuitive navigation\n• Camera integration with bulk photo capture workflows\n\n**User Management & Multi-Tenancy**\n\n• User registration and authentication with email/password\n• Property sharing capabilities with email invitations\n• White-label signup flows with custom branding and logos\n• Commercial client grouping and tagging systems\n• Role-based access control for different user types\n\n**Integration & Sync**\n\n• Bidirectional sync with existing Tugboat membership portal\n• Webhook-based property and user synchronization\n• Email notifications for upload completion and processing status\n• API endpoints for third-party integrations",
        },
        {
          id: 6,
          section_key: "non-functional-requirements",
          title: "Non-Functional Requirements",
          icon: {
            name: "Circle",
          },
          phase: "Prototype Phase",
          content:
            "**Performance Requirements**\n\n• Photo upload and processing: 100 photos in <5 minutes\n• Page load times under 2 seconds on mobile devices\n• Support for 1,000+ concurrent users during peak usage\n• 99.9% uptime availability with automated monitoring\n• Database queries under 100ms response time\n\n**Scalability & Reliability**\n\n• Horizontal scaling capabilities for millions of records\n• Auto-scaling based on upload volume and processing demand\n• Disaster recovery with automated backups\n• Multi-region deployment support for global accessibility\n• Queue-based processing for handling upload spikes\n\n**Security & Compliance**\n\n• End-to-end encryption for photo uploads and sensitive data\n• GDPR compliance for user data management\n• Secure API authentication with JWT tokens\n• Regular security audits and vulnerability assessments\n• Data retention policies aligned with legal requirements\n\n**Mobile & Usability**\n\n• Native mobile performance with Flutter framework\n• Offline functionality for photo capture and basic operations\n• Intuitive user interface requiring minimal training\n• Accessibility compliance for diverse user needs\n• Cross-platform compatibility (iOS and Android)",
        },
        {
          id: 8,
          section_key: "data-model-schemas",
          title: "Data Model & Schemas",
          icon: {
            name: "FileText",
          },
          phase: "Prototype Phase",
          content:
            "**Core Data Entities**\n\n• Users: authentication, profile data, preferences, and subscription status\n• Properties: address, nickname, cover photo, sharing permissions\n• Collections: flexible categorization replacing geospatial 'areas'\n• Items: AI-extracted metadata, user edits, photos, and valuations\n• Uploads: batch processing status, photo references, and processing logs\n• Duplicates: similarity scores, user decisions, and cleanup history\n\n**Database Design**\n\n• Normalized PostgreSQL schema for transactional data integrity\n• Optimized indexes for fast querying across millions of records\n• Foreign key relationships maintaining referential integrity\n• Audit trails for tracking changes and user actions\n\n**Key Relationships**\n\n• User-to-Property: one-to-many with sharing capabilities\n• Property-to-Collection: one-to-many organizational structure\n• Collection-to-Item: one-to-many with flexible categorization\n• Upload-to-Item: one-to-many for batch processing tracking\n• Item-to-Photo: one-to-many for multiple angles and details\n\n**Schema Evolution**\n\n• Version-controlled database migrations with rollback capabilities\n• Backward compatibility for API consumers\n• Data validation constraints and business rules\n• Performance optimization through strategic indexing",
        },
        {
          id: 9,
          section_key: "api-design",
          title: "API Design",
          icon: {
            name: "Code2",
          },
          phase: "Prototype Phase",
          content:
            "**API Architecture**\n\n• RESTful API design with consistent resource naming\n• GraphQL for complex data queries and mobile optimization\n• WebSocket connections for real-time upload progress\n• Webhook endpoints for Tugboat portal synchronization\n\n**Core API Endpoints**\n\n• Authentication: auth/login, auth/register, auth/refresh\n• Properties: properties CRUD operations with sharing\n• Collections: collections management within properties\n• Items: items with AI metadata and user editing\n• Uploads: uploads with batch processing and status tracking\n• Exports: exports for CSV/XLS/PDF generation\n• Sync: sync for Tugboat portal integration\n\n**Mobile-Optimized APIs**\n\n• Batch operations for offline sync capabilities\n• Compressed response formats for mobile bandwidth\n• Progressive image loading with multiple resolutions\n• Efficient pagination for large item collections\n\n**Integration Standards**\n\n• OpenAPI 3.0 specification for documentation\n• JWT authentication with refresh token rotation\n• Rate limiting to prevent abuse and ensure fair usage\n• Comprehensive error handling with actionable messages\n• API versioning strategy for backward compatibility",
        },
      ],
    },
    {
      id: "implementation",
      title: "Implementation",
      icon: {
        name: "Code",
      },
      sections: [
        {
          id: 7,
          section_key: "solution-architecture",
          title: "Solution Architecture",
          icon: {
            name: "FileText",
          },
          phase: "Prototype Phase",
          content:
            "Our solution architecture balances speed to market in the early phase with long-term scalability for enterprise adoption.\n\n**Frontend (FlutterFlow)**\n\nSingle codebase (mobile + responsive web) with reusable components/design tokens.\n\nMinimal Custom Actions—device access/glue only; no core logic in the client.\n\nRenders server-computed state (permissions, progress, KPIs, workflows).\n\nEvolves from capture → foundation modules → commercial features → converged portal.\n\n**Backend & Data (Nest.js + Postgres)**\n\nNest.js BFF/API in front of Postgres (self-hosted) returning view-models the FF UI can bind to.\n\nPipelines & workers: normalize → OpenAI Vision → dedup (later OCR, tagging) via queues (BullMQ + Redis or cloud tasks).\n\nMulti-tenant model, RBAC, RLS-compatible auth.\n\nContracts first: stable endpoints so the UI never breaks as services evolve.\n\n**Observability & Ops**\n\nStructured logs, tracing, metrics on pipelines; alerting on SLOs.\n\nCI/CD for Nest.js services and workers; FF publish pipeline.\n\nBlue/green or canary releases for critical services.\n\n**Key Integrations**\n\nNow: OpenAI Vision (recognition/dedup), email/SMS/push for comms.\n\nLater: OCR ingestion, BI/dashboards, insurance/partner APIs for B2B.",
        },
        {
          id: 11,
          section_key: "infrastructure-devops",
          title: "Infrastructure & DevOps",
          icon: {
            name: "Settings",
          },
          phase: "MLP Development",
          content:
            "**Vercel Platform Infrastructure**\n\n• Vercel hosting with global CDN for optimal performance\n• Vercel Firewall for DDoS protection and security\n• Rollout deployments for safe feature releases\n• Built-in CI/CD pipeline with automated testing\n• Environment-based deployments (development, staging, production)\n\n**Custom Microservices Architecture**\n\n• Containerized microservices for image processing\n• Kubernetes orchestration for scalable workloads\n• Auto-scaling based on upload volume and processing demand\n• Load balancing for high availability\n\n**Image Processing Infrastructure**\n\n• Dedicated microservice for AI analysis and deduplication\n• Queue-based processing with Redis for job management\n• Parallel processing workers for concurrent photo analysis\n• Optimized storage and retrieval for large image datasets\n\n**Monitoring & Observability**\n\n• Real-time application performance monitoring\n• Custom dashboards for upload processing metrics\n• Error tracking and alerting systems\n• User behavior analytics and conversion tracking\n\n**Security & Compliance**\n\n• Automated security scanning in CI/CD pipeline\n• Secrets management for API keys and credentials\n• Network security and access controls\n• Regular backup and disaster recovery procedures",
        },
        {
          id: 12,
          section_key: "telemetry-analytics",
          title: "Telemetry & Analytics",
          icon: {
            name: "BarChart3",
          },
          phase: "MLP Development",
          content:
            "**User Behavior Analytics**\n\n• Upload frequency and batch size tracking\n• Collection organization patterns and preferences\n• Item editing and curation behavior analysis\n• Export usage and format preferences\n• Mobile app engagement and retention metrics\n\n**Processing Performance Metrics**\n\n• AI analysis accuracy and confidence scores\n• Deduplication effectiveness and user acceptance rates\n• Processing time optimization and bottleneck identification\n• Queue management and throughput monitoring\n• Error rates and failure analysis\n\n**Business Intelligence**\n\n• User acquisition and conversion tracking\n• Property and item volume growth metrics\n• Feature adoption and usage patterns\n• Commercial client engagement and success metrics\n• Revenue attribution and subscription analytics\n\n**Operational Dashboards**\n\n• Real-time processing status and queue health\n• System performance and resource utilization\n• User support metrics and issue resolution\n• Cost optimization and resource allocation insights\n\n**Predictive Analytics**\n\n• User churn prediction and retention strategies\n• Processing capacity planning and scaling triggers\n\n• Feature usage forecasting for development prioritization\n• Market expansion opportunity identification",
        },
        {
          id: 13,
          section_key: "trust-safety",
          title: "Trust & Safety",
          icon: {
            name: "Circle",
          },
          phase: "MLP Development",
          content:
            "**Data Privacy & Protection**\n\n• End-to-end encryption for sensitive inventory data\n• Secure image storage with access controls\n• User consent management for data processing\n• GDPR compliance for European users\n• Data retention policies and automated cleanup\n\n**Content Moderation**\n\n• Automated inappropriate content detection\n• User reporting mechanisms for policy violations\n• Manual review processes for flagged content\n• Community guidelines enforcement\n\n**User Safety & Verification**\n\n• Email verification for account creation\n• Property ownership verification for commercial clients\n• Suspicious activity monitoring and alerts\n• Account security features and two-factor authentication\n\n**Platform Integrity**\n\n• Anti-spam measures for uploads and user-generated content\n• Fraud detection for commercial account registrations\n• Rate limiting to prevent system abuse\n• Quality assurance for AI-generated metadata\n\n**Incident Response**\n\n• Security incident response procedures\n• Data breach notification protocols\n• User support escalation processes\n• Legal compliance and reporting requirements",
        },
        {
          id: 14,
          section_key: "compliance-privacy",
          title: "Compliance & Privacy",
          icon: {
            name: "Circle",
          },
          phase: "MLP Development",
          content:
            "**Data Privacy Regulations**\n\n• GDPR compliance for European property owners\n• CCPA compliance for California residents\n• Data minimization principles for inventory processing\n• User consent management for AI analysis\n• Right to data portability and deletion\n\n**Insurance Industry Compliance**\n\n• Data security standards for insurance documentation\n• Audit trail requirements for claims processing\n• Document retention policies aligned with insurance regulations\n• Privacy protection for sensitive property information\n\n**Mobile App Compliance**\n\n• App store privacy policy requirements (iOS/Android)\n• Mobile data collection transparency\n• Camera and storage permission management\n• Offline data handling and sync compliance\n\n**Commercial Client Requirements**\n\n• Enterprise security standards for HOA and commercial clients\n• Multi-tenant data isolation and privacy\n\n• White-label compliance and branding guidelines\n• Service level agreements and data processing terms\n\n**International Considerations**\n\n• Cross-border data transfer compliance\n• Regional privacy law variations\n• Currency and tax compliance for global operations\n• Accessibility standards for diverse user populations",
        },
      ],
    },
    {
      id: "business-operations",
      title: "Business & Operations",
      icon: {
        name: "BarChart3",
      },
      sections: [
        {
          id: 15,
          section_key: "commerce-operating-model",
          title: "Operating Model",
          icon: {
            name: "DollarSign",
          },
          phase: "MLP Development",
          content:
            "**Revenue Model Suggestions**\n\n• B2C Subscriptions: $50-100/month for residential property owners\n• Commercial Contracts: $500-2000/month for HOAs and property managers\n• White-label Setup: $10,000+ one-time fees plus recurring revenue\n• Inventory Services: B2B SaaS model for professional inventory companies\n• Export Premium: Advanced reporting and analytics features\n\n**Service Operations**\n\n• User onboarding and training programs\n• Customer success management for commercial clients\n• Technical support and troubleshooting\n\n• Data migration from existing systems\n• White-label customization and branding services\n\n**Platform Operations**\n\n• AI processing quality assurance and improvement\n• Database maintenance and optimization\n• Security monitoring and incident response\n• Performance optimization and scaling\n• Feature development and enhancement\n\n**Partnership Strategy**\n\n• Insurance carrier integrations and partnerships\n• Property management company alliances\n• Technology vendor relationships\n• Professional services network development",
        },
        {
          id: 16,
          section_key: "go-to-market-plan",
          title: "Go-to-Market Plan",
          icon: {
            name: "TrendingUp",
          },
          phase: "MLP Development",
          content:
            "**Market Entry Strategy**\n\n• Migrate existing 800+ Tugboat members to new platform\n• Leverage proven value proposition from current proof-of-concept\n• Target ultra-high-net-worth individuals and commercial properties\n• Focus on insurance claims preparation and asset documentation\n\n**Target Audience Expansion**\n\n• Primary: Existing Tugboat membership base\n• Secondary: High-net-worth homeowners and property investors\n• Commercial: HOAs, property management companies, apartment complexes\n• Professional: Inventory service providers and insurance adjusters\n\n**Marketing Channels**\n\n• Direct migration from existing Tugboat platform\n• Insurance industry partnerships and referrals\n• Property management trade associations\n• Digital marketing targeting property owners\n• Professional services network development\n\n**Launch Phases**\n\n• Phase 1: Beta migration of existing users (4 weeks)\n• Phase 2: Commercial pilot with 3-5 HOA clients (8 weeks)\n• Phase 3: Public launch and marketing campaign (12 weeks)\n• Phase 4: Scale and market expansion (ongoing)\n\n**Success Metrics**\n\n• 90%+ existing user migration rate\n• 3+ commercial contracts within 6 months\n• 1,500+ total users by end of Year 1\n• <5 minute processing time achievement",
        },
        {
          id: 18,
          section_key: "team-resourcing",
          title: "Team & Resourcing",
          icon: {
            name: "Users",
          },
          phase: "MLP Development",
          content:
            "**Core Development Team**\n\n• Project Manager  - Coordination and stakeholder communication\n• Flutter Developer  - Mobile app development and UI/UX\n• Backend Developer  - API development and microservices\n• AI/ML Engineer  - OpenAI integration and deduplication algorithms\n• DevOps Engineer  - Vercel deployment and infrastructure\n\n**Specialized Support**\n\n• UI/UX Designer  - Mobile-first design optimization\n• QA Engineer  - Testing and quality assurance\n**External Resources**\n\n• Tugboat team collaboration and domain expertise\n• OpenAI API and technical support\n• Vercel platform support and optimization\n• Legal consultation for compliance and privacy\n\n**Team Management**\n\n• Daily standups and progress tracking\n• Weekly stakeholder demos and feedback sessions\n• Bi-weekly sprint planning and retrospectives\n• Continuous integration and deployment practices\n\n**Knowledge Transfer**\n\n• Comprehensive documentation and code comments\n• Video tutorials for platform administration\n• Technical handoff sessions with Tugboat team\n• Ongoing support and maintenance planning",
        },
      ],
    },
    {
      id: "quality-governance",
      title: "Quality & Governance",
      icon: {
        name: "Shield",
      },
      sections: [
        {
          id: 19,
          section_key: "quality-test-strategy",
          title: "Quality & Test Strategy",
          icon: {
            name: "CheckSquare",
          },
          phase: "MLP Development",
          content:
            "**Mobile App Testing**\n\n• Flutter widget testing for UI components\n• Integration testing for API connectivity\n• Device testing across iOS and Android platforms\n• Performance testing for photo upload and processing\n• Offline functionality and sync testing\n\n**AI Processing Validation**\n\n• Object detection accuracy testing with diverse photo sets\n• Deduplication algorithm effectiveness measurement\n• Processing time benchmarking and optimization\n• Edge case handling for unusual or damaged items\n• Metadata extraction quality assurance\n\n**Backend & API Testing**\n\n• Unit testing for business logic and data processing\n• API endpoint testing with automated test suites\n• Database performance testing under load\n• Security testing for authentication and authorization\n• Integration testing with third-party services\n\n**User Experience Testing**\n\n• Usability testing with target user groups\n• Accessibility testing for diverse user needs\n• Cross-platform consistency validation\n• Performance testing on various device specifications\n• User acceptance testing with Tugboat team\n\n**Quality Metrics**\n\n• 90%+ code coverage for critical components\n• <5 minute processing time for 100 photos\n• 99.9% uptime availability\n• <2 second app response times\n• 95%+ user satisfaction scores",
        },
        {
          id: 20,
          section_key: "risk-register",
          title: "Risk Register",
          icon: {
            name: "AlertTriangle",
          },
          phase: "Design Thinking",
          content:
            "**Technical Risks**\n\n• Flutter performance challenges with large photo datasets\n**Business Risks**\n\n• User migration resistance from existing no-code platform\n• Commercial client acquisition slower than projected\n• Competition from established property management solutions\n• Insurance industry adoption barriers\n• Regulatory changes affecting data processing requirements\n\n**Operational Risks**\n\n• AI processing costs exceeding budget projections\n• Data migration complexity from existing Airtable system\n• Key team member availability during critical development phases\n• Third-party service dependencies and vendor lock-in\n• Security incidents affecting user trust and data integrity\n\n**Mitigation Strategies**\n\n• Multiple AI provider evaluation and fallback options\n• Phased user migration with parallel system operation\n• Comprehensive testing with diverse photo datasets\n• Cost monitoring and optimization for AI processing\n• Regular security audits and incident response planning\n• Clear communication and change management for existing users",
        },
        {
          id: 22,
          section_key: "appendices",
          title: "Appendices",
          icon: {
            name: "FileText",
          },
          phase: "MLP Development",
          content:
            "**Technical Appendices**\n\n• API documentation and endpoint specifications\n• Database schema diagrams and relationships\n• AI processing workflow diagrams\n• Security architecture and compliance documentation\n• Performance benchmarking results and optimization strategies\n\n**Business Appendices**\n\n• Market research and competitive analysis\n• User persona definitions and journey maps\n• Financial projections and ROI calculations\n• Legal and compliance documentation\n• Partnership agreements and vendor contracts\n\n**Project Appendices**\n\n• Detailed project timeline and milestone definitions\n• Resource allocation and team structure diagrams\n• Risk assessment matrices and mitigation plans\n• Quality assurance checklists and testing protocols\n• Change management and communication plans",
        },
      ],
    },
  ],
}

export interface Phase {
  id: string
  name: string
  subtitle: string
  storyPointsMin: number
  storyPointsMax: number
  scope: string[]
  outcome: string
  icon: LucideIcon
}

export const phases: Phase[] = [
  {
    id: "poc-migration",
    name: "Full-Stack Migration + Mobile Capture App",
    subtitle: "Foundation & Technical Migration",
    storyPointsMin: 220,
    storyPointsMax: 240,
    scope: [
      "Backend: Nest.js BFF/API over Postgres (Supabase-hosted), with RLS-compatible auth, file storage, and image pipeline (normalize → OpenAI Vision → in-batch dedup) as workers/edge functions.",
      "Frontend: FlutterFlow capture app (login, collection select, capture/upload, progress) using stable API contracts; Retain Noloco as interim admin UI for data management.",
      "Ops: Basic observability (logs/metrics), error handling, and CI/CD.",
    ],
    outcome:
      "Market-ready foundation on a permanent stack. Stable API contracts to support future phases. Core KPI: 100 photos processed in <5 minutes.",
    icon: Rocket,
  },
  {
    id: "foundation-scale",
    name: "Foundation & Multi-Tenant",
    subtitle: "Multi-User & Performance Optimization",
    storyPointsMin: 100,
    storyPointsMax: 120,
    scope: [
      "Frontend: Expand FF app with offline queueing, roles/permissions, and white-label themes.",
      "Backend: Multi-tenant org model, RBAC enforcement, queue workers for cross-collection dedup, audit logs.",
    ],
    outcome: "Enterprise-ready app + tenancy; ready for pilot onboarding (HOAs/service providers).",
    icon: Building,
  },
  {
    id: "commercial-enablement",
    name: "Commercial Enablement",
    subtitle: "Enterprise Features & B2B Integration",
    storyPointsMin: 120,
    storyPointsMax: 150,
    scope: [
      "Frontend: Dashboards, annotation/collaboration, OCR review flows in FF.",
      "Backend: OCR pipeline, reporting API, partner/B2B integrations, usage metering/billing hooks",
    ],
    outcome: "Fully revenue-ready platform; supports HOA/enterprise contracts and partner scenarios.",
    icon: Handshake,
  },
  {
    id: "platform-evolution",
    name: "Platform Evolution",
    subtitle: "AI Automation & Market Leadership",
    storyPointsMin: 75,
    storyPointsMax: 90,
    scope: [
      "Frontend: Portal convergence (member + admin) in FF with feature flags for enterprise variants.",
      "Backend: AI automation microservices (semantic tagging, anomaly detection), compliance (audit trails, data residency), deep observability",
    ],
    outcome: "Competitive moat via AI + compliance; scale with confidence.",
    icon: Shield,
  },
]

