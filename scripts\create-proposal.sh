#!/usr/bin/env bash
set -euo pipefail

if [ $# -lt 1 ]; then
  echo "Usage: $0 <lead>" >&2
  exit 1
fi

LEAD="$1"
REPO_URL="${REPO_URL:-$(git config --get remote.origin.url 2>/dev/null)}"
if [ -z "$REPO_URL" ]; then
  echo "Repository URL not found. Set REPO_URL or run inside a git repo with an origin." >&2
  exit 1
fi

BRANCH="proposal/${LEAD}"
CLONE_DIR="proposal-${LEAD}"

# Clone the template branch
if [ -d "$CLONE_DIR" ]; then
  echo "Directory $CLONE_DIR already exists" >&2
  exit 1
fi

# Allow overriding the template branch via TEMPLATE_BRANCH, default to structured-template
TEMPLATE_BRANCH="${TEMPLATE_BRANCH:-structured-template}"
git clone --branch "$TEMPLATE_BRANCH" "$REPO_URL" "$CLONE_DIR"
cd "$CLONE_DIR"

# Generate platform configuration from lead data
npm install
npm run ingest -- "leads/${LEAD}.json"

# Commit the generated configuration and push to remote
git checkout -b "$BRANCH"
git add lib/platform-config.ts
if git commit -m "chore: generate proposal for ${LEAD}"; then
  git push -u origin "$BRANCH"
  echo "Proposal branch pushed: $BRANCH"
else
  echo "No changes to commit" >&2
fi
