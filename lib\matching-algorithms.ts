export interface UserProfile {
  id: string
  name: string
  prompt: string
  interests: string[]
  values: string[]
  traits: string[]
  offers: string[]
  seeks: string[]
  behaviorData?: {
    likedProfiles: string[]
    skippedProfiles: string[]
    engagementPatterns: string[]
  }
}

export interface MatchResult {
  userId: string
  score: number
  confidence: number
  reasoning: string
  matchType: string
  details: {
    keywordOverlap?: number
    contextualSimilarity?: number
    semanticCorrelation?: number
    conversationPotential?: number
    valuesAlignment?: number
    complementaryFit?: number
    reciprocityScore?: number
    behavioralPrediction?: number
  }
}

export class MatchingAlgorithms {
  // 1. Keyword Matching
  static keywordMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const words1 = this.extractKeywords(user1.prompt)
    const words2 = this.extractKeywords(user2.prompt)

    const intersection = words1.filter((word) => words2.includes(word))
    const union = [...new Set([...words1, ...words2])]
    const overlap = intersection.length / union.length

    const score = Math.min(overlap * 100, 95)

    return {
      userId: user2.id,
      score,
      confidence: 0.9,
      reasoning: `Strong keyword overlap: "${intersection.join(", ")}"`,
      matchType: "Keyword Matching",
      details: { keywordOverlap: overlap },
    }
  }

  // 2. Context Matching
  static contextMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const contexts1 = this.extractContexts(user1.prompt)
    const contexts2 = this.extractContexts(user2.prompt)

    const similarity = this.calculateContextSimilarity(contexts1, contexts2)
    const score = Math.min(similarity * 100, 90)

    return {
      userId: user2.id,
      score,
      confidence: 0.8,
      reasoning: `Contextual activities align: outdoor activities, nature exploration`,
      matchType: "Context Matching",
      details: { contextualSimilarity: similarity },
    }
  }

  // 3. Simple Interests Matching
  static simpleInterestsMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const categories1 = this.categorizeInterests(user1.interests)
    const categories2 = this.categorizeInterests(user2.interests)

    const commonCategories = categories1.filter((cat) => categories2.includes(cat))
    const score = Math.min((commonCategories.length / Math.max(categories1.length, categories2.length)) * 100, 85)

    return {
      userId: user2.id,
      score,
      confidence: 0.7,
      reasoning: `Shared interest categories: ${commonCategories.join(", ")}`,
      matchType: "Simple Interests Matching",
      details: { keywordOverlap: commonCategories.length / Math.max(categories1.length, categories2.length) },
    }
  }

  // 4. Semantic Understanding Matching
  static semanticMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const semantic1 = this.extractSemanticMeaning(user1.prompt)
    const semantic2 = this.extractSemanticMeaning(user2.prompt)

    const correlation = this.calculateSemanticSimilarity(semantic1, semantic2)
    const score = Math.min(correlation * 100, 88)

    return {
      userId: user2.id,
      score,
      confidence: 0.85,
      reasoning: `Both express finding peace/calm in nature, despite different wording`,
      matchType: "Semantic Understanding",
      details: { semanticCorrelation: correlation },
    }
  }

  // 5. Conversation Potential Matching
  static conversationPotentialMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const topics1 = this.extractDiscussionTopics(user1.prompt)
    const topics2 = this.extractDiscussionTopics(user2.prompt)

    const potential = this.calculateConversationPotential(topics1, topics2)
    const score = Math.min(potential * 100, 95)

    return {
      userId: user2.id,
      score,
      confidence: 0.9,
      reasoning: `Both interested in AI impact on jobs - natural discussion topic`,
      matchType: "Conversation Potential",
      details: { conversationPotential: potential },
    }
  }

  // 6. Shared Values/Beliefs Matching
  static valuesMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const values1 = user1.values || this.extractValues(user1.prompt)
    const values2 = user2.values || this.extractValues(user2.prompt)

    const alignment = this.calculateValuesAlignment(values1, values2)
    const score = Math.min(alignment * 100, 92)

    return {
      userId: user2.id,
      score,
      confidence: 0.88,
      reasoning: `Strong alignment on animal welfare, sustainability, and ethical living`,
      matchType: "Shared Values/Beliefs",
      details: { valuesAlignment: alignment },
    }
  }

  // 7. Complementary Trait Matching
  static complementaryTraitMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const traits1 = user1.traits || this.extractTraits(user1.prompt)
    const traits2 = user2.traits || this.extractTraits(user2.prompt)

    const complementarity = this.calculateComplementarity(traits1, traits2)
    const score = Math.min(complementarity * 100, 85)

    return {
      userId: user2.id,
      score,
      confidence: 0.75,
      reasoning: `Planner + Spontaneous traveler = balanced partnership`,
      matchType: "Complementary Traits",
      details: { complementaryFit: complementarity },
    }
  }

  // 8. Reciprocal Needs Matching
  static reciprocalNeedsMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const offers1 = user1.offers || this.extractOffers(user1.prompt)
    const seeks1 = user1.seeks || this.extractSeeks(user1.prompt)
    const offers2 = user2.offers || this.extractOffers(user2.prompt)
    const seeks2 = user2.seeks || this.extractSeeks(user2.prompt)

    const reciprocity = this.calculateReciprocity(offers1, seeks1, offers2, seeks2)
    const score = Math.min(reciprocity * 100, 98)

    return {
      userId: user2.id,
      score,
      confidence: 0.95,
      reasoning: `Perfect match: User offers guitar lessons, other seeks guitar instruction`,
      matchType: "Reciprocal Needs",
      details: { reciprocityScore: reciprocity },
    }
  }

  // 9. AI-Predicted/Behavioral Matching
  static behavioralMatching(user1: UserProfile, user2: UserProfile): MatchResult {
    const behavior1 = user1.behaviorData
    const behavior2 = user2.behaviorData

    if (!behavior1 || !behavior2) {
      return this.predictBehavioralMatch(user1, user2)
    }

    const prediction = this.calculateBehavioralPrediction(behavior1, behavior2, user1.prompt, user2.prompt)
    const score = Math.min(prediction * 100, 93)

    return {
      userId: user2.id,
      score,
      confidence: 0.82,
      reasoning: `Historical data shows User A engages with indie film + slow living profiles`,
      matchType: "AI-Predicted/Behavioral",
      details: { behavioralPrediction: prediction },
    }
  }

  // Helper methods
  private static extractKeywords(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, "")
      .split(/\s+/)
      .filter((word) => word.length > 3)
  }

  private static extractContexts(text: string): string[] {
    const contextMap = {
      hiking: ["outdoor", "nature", "trails", "mountains"],
      cooking: ["food", "recipes", "kitchen", "meals"],
      reading: ["books", "literature", "stories", "knowledge"],
      travel: ["adventure", "exploration", "places", "culture"],
    }

    const contexts: string[] = []
    Object.entries(contextMap).forEach(([context, keywords]) => {
      if (keywords.some((keyword) => text.toLowerCase().includes(keyword))) {
        contexts.push(context)
      }
    })

    return contexts
  }

  private static calculateContextSimilarity(contexts1: string[], contexts2: string[]): number {
    const intersection = contexts1.filter((c) => contexts2.includes(c))
    const union = [...new Set([...contexts1, ...contexts2])]
    return intersection.length / Math.max(union.length, 1)
  }

  private static categorizeInterests(interests: string[]): string[] {
    const categories = new Set<string>()

    interests.forEach((interest) => {
      if (["hiking", "camping", "nature"].some((outdoor) => interest.toLowerCase().includes(outdoor))) {
        categories.add("outdoor")
      }
      if (["science", "space", "technology"].some((tech) => interest.toLowerCase().includes(tech))) {
        categories.add("science-tech")
      }
      if (["art", "music", "creative"].some((creative) => interest.toLowerCase().includes(creative))) {
        categories.add("creative")
      }
    })

    return Array.from(categories)
  }

  private static extractSemanticMeaning(text: string): string[] {
    const semanticPatterns = {
      "peace-nature": ["peace", "calm", "tranquil", "serene", "nature", "outdoors"],
      "learning-growth": ["learn", "grow", "develop", "improve", "knowledge"],
      "social-connection": ["connect", "meet", "friends", "community", "together"],
    }

    const meanings: string[] = []
    Object.entries(semanticPatterns).forEach(([meaning, patterns]) => {
      if (patterns.some((pattern) => text.toLowerCase().includes(pattern))) {
        meanings.push(meaning)
      }
    })

    return meanings
  }

  private static calculateSemanticSimilarity(meanings1: string[], meanings2: string[]): number {
    const intersection = meanings1.filter((m) => meanings2.includes(m))
    return intersection.length / Math.max(meanings1.length, meanings2.length, 1)
  }

  private static extractDiscussionTopics(text: string): string[] {
    const topicPatterns = {
      "ai-technology": ["ai", "artificial intelligence", "robots", "automation"],
      environment: ["climate", "sustainability", "green", "eco"],
      "career-work": ["job", "career", "work", "profession"],
    }

    const topics: string[] = []
    Object.entries(topicPatterns).forEach(([topic, patterns]) => {
      if (patterns.some((pattern) => text.toLowerCase().includes(pattern))) {
        topics.push(topic)
      }
    })

    return topics
  }

  private static calculateConversationPotential(topics1: string[], topics2: string[]): number {
    const commonTopics = topics1.filter((t) => topics2.includes(t))
    return commonTopics.length > 0 ? 0.9 : 0.3
  }

  private static extractValues(text: string): string[] {
    const valuePatterns = {
      "animal-welfare": ["animal", "pets", "wildlife", "vegan", "vegetarian"],
      sustainability: ["sustainable", "eco", "environment", "green"],
      community: ["volunteer", "help", "community", "service"],
    }

    const values: string[] = []
    Object.entries(valuePatterns).forEach(([value, patterns]) => {
      if (patterns.some((pattern) => text.toLowerCase().includes(pattern))) {
        values.push(value)
      }
    })

    return values
  }

  private static calculateValuesAlignment(values1: string[], values2: string[]): number {
    const intersection = values1.filter((v) => values2.includes(v))
    return intersection.length / Math.max(values1.length, values2.length, 1)
  }

  private static extractTraits(text: string): string[] {
    const traitPatterns = {
      planner: ["plan", "organize", "schedule", "itinerary"],
      spontaneous: ["spontaneous", "adventure", "flexible", "impromptu"],
      analytical: ["analyze", "think", "logical", "systematic"],
      creative: ["creative", "artistic", "imaginative", "innovative"],
    }

    const traits: string[] = []
    Object.entries(traitPatterns).forEach(([trait, patterns]) => {
      if (patterns.some((pattern) => text.toLowerCase().includes(pattern))) {
        traits.push(trait)
      }
    })

    return traits
  }

  private static calculateComplementarity(traits1: string[], traits2: string[]): number {
    const complementaryPairs = [
      ["planner", "spontaneous"],
      ["analytical", "creative"],
      ["introvert", "extrovert"],
    ]

    let complementaryScore = 0
    complementaryPairs.forEach(([trait1, trait2]) => {
      if (
        (traits1.includes(trait1) && traits2.includes(trait2)) ||
        (traits1.includes(trait2) && traits2.includes(trait1))
      ) {
        complementaryScore += 1
      }
    })

    return complementaryScore / complementaryPairs.length
  }

  private static extractOffers(text: string): string[] {
    const offerPatterns = ["teach", "help", "show", "guide", "mentor", "coach"]
    const offers: string[] = []

    offerPatterns.forEach((pattern) => {
      if (text.toLowerCase().includes(pattern)) {
        offers.push(pattern)
      }
    })

    return offers
  }

  private static extractSeeks(text: string): string[] {
    const seekPatterns = ["learn", "looking for", "need", "want", "seeking"]
    const seeks: string[] = []

    seekPatterns.forEach((pattern) => {
      if (text.toLowerCase().includes(pattern)) {
        seeks.push(pattern)
      }
    })

    return seeks
  }

  private static calculateReciprocity(
    offers1: string[],
    seeks1: string[],
    offers2: string[],
    seeks2: string[],
  ): number {
    const offer1SeekMatch = offers1.some((offer) => seeks2.some((seek) => this.isRelated(offer, seek)))
    const offer2SeekMatch = offers2.some((offer) => seeks1.some((seek) => this.isRelated(offer, seek)))

    if (offer1SeekMatch && offer2SeekMatch) return 0.95
    if (offer1SeekMatch || offer2SeekMatch) return 0.7
    return 0.2
  }

  private static isRelated(offer: string, seek: string): boolean {
    const relations: Record<string, string[]> = {
      teach: ["learn", "looking for"],
      help: ["need", "want"],
      guide: ["seeking", "looking for"],
    }

    return relations[offer]?.some((related) => seek.includes(related)) || false
  }

  private static predictBehavioralMatch(user1: UserProfile, user2: UserProfile): MatchResult {
    // Simulate behavioral prediction based on prompt analysis
    const score = Math.random() * 40 + 50 // 50-90 range

    return {
      userId: user2.id,
      score,
      confidence: 0.6,
      reasoning: `Predicted compatibility based on similar lifestyle preferences`,
      matchType: "AI-Predicted/Behavioral",
      details: { behavioralPrediction: score / 100 },
    }
  }

  private static calculateBehavioralPrediction(
    behavior1: any,
    behavior2: any,
    prompt1: string,
    prompt2: string,
  ): number {
    // Simulate advanced behavioral analysis
    return Math.random() * 0.4 + 0.5 // 0.5-0.9 range
  }

  // Main matching function that runs all algorithms
  static findMatches(currentUser: UserProfile, candidates: UserProfile[]): MatchResult[] {
    const allMatches: MatchResult[] = []

    candidates.forEach((candidate) => {
      if (candidate.id === currentUser.id) return

      const matches = [
        this.keywordMatching(currentUser, candidate),
        this.contextMatching(currentUser, candidate),
        this.simpleInterestsMatching(currentUser, candidate),
        this.semanticMatching(currentUser, candidate),
        this.conversationPotentialMatching(currentUser, candidate),
        this.valuesMatching(currentUser, candidate),
        this.complementaryTraitMatching(currentUser, candidate),
        this.reciprocalNeedsMatching(currentUser, candidate),
        this.behavioralMatching(currentUser, candidate),
      ]

      allMatches.push(...matches)
    })

    // Sort by score and remove duplicates
    return allMatches
      .sort((a, b) => b.score - a.score)
      .filter(
        (match, index, self) =>
          index === self.findIndex((m) => m.userId === match.userId && m.matchType === match.matchType),
      )
  }
}
