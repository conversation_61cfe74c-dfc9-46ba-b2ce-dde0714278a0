{
  "branding": {
    "logo": "{{logo}}",
    "accentColor": "{{accentColor}}",
    "companyName": "{{companyName}}",
    "companyDescription": "{{companyDescription}}"
  },
  "variables": {
    "projectName": "{{projectName}}",
    "timeline": "{{timeline}}",
    "budget": "{{budget}}",
    "expectedROI": "{{expectedROI}}",
    "marketSize": "{{marketSize}}",
    "developmentWeeks": "{{developmentWeeks}}"
  },
  "content": {
    "proposalUrl": "{{proposalUrl}}",
    "hero": {
      "title": "{{heroTitle}}",
      "subtitle": "{{heroSubtitle}}",
      "description": "{{heroDescription}}",
      "primaryCTA": "{{heroPrimaryCTA}}",
      "secondaryCTA": "{{heroSecondaryCTA}}",
      "features": ["{{feature1}}", "{{feature2}}", "{{feature3}}", "{{feature4}}"]
    },
    "statistics": [
      {
        "id": 1,
        "value": "{{stat1Value}}",
        "label": "{{stat1Label}}",
        "icon": {"name": "{{stat1IconName}}"},
        "color": "{{stat1Color}}"
      }
    ],
    "methodology": {
      "title": "{{methodologyTitle}}",
      "description": "{{methodologyDescription}}"
    },
    "cta": {
      "title": "{{ctaTitle}}",
      "description": "{{ctaDescription}}",
      "buttonText": "{{ctaButtonText}}"
    }
  },
  "macroSections": [
    {
      "id": "{{macro1Id}}",
      "title": "{{macro1Title}}",
      "icon": {"name": "{{macro1IconName}}"},
      "sections": [
        {
          "id": 1,
          "section_key": "{{section1Key}}",
          "title": "{{section1Title}}",
          "icon": {"name": "{{section1IconName}}"},
          "phase": "{{section1Phase}}",
          "content": "{{section1Content}}"
        }
      ],
    }
  ]
}
