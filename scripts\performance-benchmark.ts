// Performance Benchmarking Script for PEP Portal
// Tests database query performance, chatbot response times, and UI rendering

interface PerformanceMetric {
  operation: string
  duration: number
  timestamp: number
  success: boolean
  details?: any
}

class PerformanceBenchmark {
  private metrics: PerformanceMetric[] = []

  async benchmarkDatabaseQueries(): Promise<void> {
    console.log("🔍 Benchmarking Database Query Performance...")

    // Test 1: Single section retrieval
    await this.measureOperation("Single Section Query", async () => {
      const response = await fetch("/api/sections/1")
      if (!response.ok) throw new Error("Query failed")
      return await response.json()
    })

    // Test 2: All sections retrieval
    await this.measureOperation("All Sections Query", async () => {
      const response = await fetch("/api/sections")
      if (!response.ok) throw new Error("Query failed")
      return await response.json()
    })

    // Test 3: Search query
    await this.measureOperation("Search Query", async () => {
      const response = await fetch("/api/search?q=executive")
      if (!response.ok) throw new Error("Search failed")
      return await response.json()
    })

    // Test 4: Chat interaction logging
    await this.measureOperation("Chat Interaction Insert", async () => {
      const response = await fetch("/api/chat-log", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          session_id: `perf_test_${Date.now()}`,
          user_message: "Performance test message",
          ai_response: "Performance test response",
          section_context: "test",
        }),
      })
      if (!response.ok) throw new Error("Insert failed")
      return await response.json()
    })
  }

  async benchmarkChatbotPerformance(): Promise<void> {
    console.log("🤖 Benchmarking Chatbot Performance...")

    const testQuestions = [
      "What is the executive summary?",
      "How much does this project cost?",
      "What's the timeline for development?",
      "Why should I choose Singular Agency?",
      "What are the technical requirements?",
    ]

    for (const question of testQuestions) {
      await this.measureOperation(`Chatbot Response: "${question.substring(0, 30)}..."`, async () => {
        const response = await fetch("/api/chat", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            messages: [{ role: "user", content: question }],
            activeSection: 1,
          }),
        })
        if (!response.ok) throw new Error("Chatbot request failed")
        return await response.json()
      })
    }
  }

  async benchmarkConcurrentUsers(): Promise<void> {
    console.log("👥 Benchmarking Concurrent User Performance...")

    const concurrentRequests = 10
    const promises = []

    for (let i = 0; i < concurrentRequests; i++) {
      promises.push(
        this.measureOperation(`Concurrent Request ${i + 1}`, async () => {
          const response = await fetch("/api/sections")
          if (!response.ok) throw new Error("Concurrent request failed")
          return await response.json()
        }),
      )
    }

    await Promise.all(promises)
  }

  private async measureOperation(name: string, operation: () => Promise<any>): Promise<void> {
    const startTime = performance.now()
    const timestamp = Date.now()

    try {
      const result = await operation()
      const duration = performance.now() - startTime

      this.metrics.push({
        operation: name,
        duration,
        timestamp,
        success: true,
        details: result,
      })

      console.log(`✅ ${name}: ${duration.toFixed(2)}ms`)
    } catch (error) {
      const duration = performance.now() - startTime

      this.metrics.push({
        operation: name,
        duration,
        timestamp,
        success: false,
        details: error instanceof Error ? error.message : "Unknown error",
      })

      console.log(`❌ ${name}: ${duration.toFixed(2)}ms (FAILED)`)
    }
  }

  generatePerformanceReport(): void {
    console.log("\n" + "=".repeat(60))
    console.log("📊 PERFORMANCE BENCHMARK RESULTS")
    console.log("=".repeat(60))

    const successfulMetrics = this.metrics.filter((m) => m.success)
    const failedMetrics = this.metrics.filter((m) => !m.success)

    if (successfulMetrics.length > 0) {
      const avgDuration = successfulMetrics.reduce((sum, m) => sum + m.duration, 0) / successfulMetrics.length
      const minDuration = Math.min(...successfulMetrics.map((m) => m.duration))
      const maxDuration = Math.max(...successfulMetrics.map((m) => m.duration))

      console.log(`📈 Successful Operations: ${successfulMetrics.length}`)
      console.log(`⏱️  Average Response Time: ${avgDuration.toFixed(2)}ms`)
      console.log(`🚀 Fastest Response: ${minDuration.toFixed(2)}ms`)
      console.log(`🐌 Slowest Response: ${maxDuration.toFixed(2)}ms`)

      // Performance thresholds
      const excellentThreshold = 100
      const goodThreshold = 300
      const acceptableThreshold = 1000

      const excellent = successfulMetrics.filter((m) => m.duration < excellentThreshold).length
      const good = successfulMetrics.filter(
        (m) => m.duration >= excellentThreshold && m.duration < goodThreshold,
      ).length
      const acceptable = successfulMetrics.filter(
        (m) => m.duration >= goodThreshold && m.duration < acceptableThreshold,
      ).length
      const slow = successfulMetrics.filter((m) => m.duration >= acceptableThreshold).length

      console.log(`\n🎯 Performance Distribution:`)
      console.log(`  🟢 Excellent (<100ms): ${excellent}`)
      console.log(`  🟡 Good (100-300ms): ${good}`)
      console.log(`  🟠 Acceptable (300-1000ms): ${acceptable}`)
      console.log(`  🔴 Slow (>1000ms): ${slow}`)
    }

    if (failedMetrics.length > 0) {
      console.log(`\n❌ Failed Operations: ${failedMetrics.length}`)
      failedMetrics.forEach((m) => {
        console.log(`  - ${m.operation}: ${m.details}`)
      })
    }

    console.log(`\n🏆 PERFORMANCE GRADE:`)
    const successRate = (successfulMetrics.length / this.metrics.length) * 100
    const avgResponseTime =
      successfulMetrics.length > 0
        ? successfulMetrics.reduce((sum, m) => sum + m.duration, 0) / successfulMetrics.length
        : 0

    let grade = "F"
    if (successRate >= 95 && avgResponseTime < 200) grade = "A+"
    else if (successRate >= 90 && avgResponseTime < 300) grade = "A"
    else if (successRate >= 85 && avgResponseTime < 500) grade = "B"
    else if (successRate >= 80 && avgResponseTime < 1000) grade = "C"
    else if (successRate >= 70) grade = "D"

    console.log(`  Grade: ${grade}`)
    console.log(`  Success Rate: ${successRate.toFixed(1)}%`)
    console.log(`  Avg Response: ${avgResponseTime.toFixed(2)}ms`)

    console.log(`\n💡 RECOMMENDATIONS:`)
    if (grade === "A+" || grade === "A") {
      console.log(`  ✅ Excellent performance! Ready for production.`)
    } else if (grade === "B") {
      console.log(`  ⚡ Good performance with room for optimization.`)
    } else {
      console.log(`  🔧 Performance improvements needed before production.`)
      console.log(`  📊 Consider database indexing and query optimization.`)
      console.log(`  🚀 Implement caching strategies for frequently accessed data.`)
    }
  }

  async runFullBenchmark(): Promise<void> {
    console.log("🚀 Starting Performance Benchmark Suite...")

    await this.benchmarkDatabaseQueries()
    await this.benchmarkChatbotPerformance()
    await this.benchmarkConcurrentUsers()

    this.generatePerformanceReport()
  }
}

// Execute performance benchmarks
async function runPerformanceBenchmarks() {
  const benchmark = new PerformanceBenchmark()
  await benchmark.runFullBenchmark()
}

export { PerformanceBenchmark, runPerformanceBenchmarks }
