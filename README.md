# PEP-Template

*Automatically synced with your [v0.app](https://v0.app) deployments*

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/singular-projects-f4874352/v0-pep-template)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.app-black?style=for-the-badge)](https://v0.app/chat/projects/k8ag8Hyj8Pd)

## Overview

This repository will stay in sync with your deployed chats on [v0.app](https://v0.app).
Any changes you make to your deployed app will be automatically pushed to this repository from [v0.app](https://v0.app).

## Deployment

Your project is live at:

**[https://vercel.com/singular-projects-f4874352/v0-pep-template](https://vercel.com/singular-projects-f4874352/v0-pep-template)**

## Build your app

Continue building your app on:

**[https://v0.app/chat/projects/k8ag8Hyj8Pd](https://v0.app/chat/projects/k8ag8Hyj8Pd)**

## How It Works

1. Create and modify your project using [v0.app](https://v0.app)
2. Deploy your chats from the v0 interface
3. Changes are automatically pushed to this repository
4. Vercel deploys the latest version from this repository

## Directory Structure

```
├── app/                 # Next.js routes and pages
├── components/          # Reusable UI components
├── leads/               # Individual lead JSON files
├── lib/                 # Shared libraries and utilities
├── scripts/             # Helper scripts such as lead ingestion
├── public/              # Static assets
└── README.md
```

## Lead Ingestion

Lead information can be captured as JSON documents. A JSON schema at `scripts/lead-config.schema.json` defines the required structure and data types. Use `leads/lead-template.json` as a starting point and replace the placeholder values (for example `{{projectName}}` and `{{budget}}`) with real data. Validate new lead files against the schema before ingestion to ensure consistency.

### Configuration Keys

| Key | Purpose |
| --- | ------- |
| `branding` | Visual identity for the lead including logo, accent color and company details. |
| `variables` | Project variables such as `projectName`, `timeline` and `budget`. |
| `content` | Marketing copy for hero, statistics, methodology and call‑to‑action sections. |
| `macroSections` | Ordered groups of proposal sections with nested subsections and content. |

### Crafting `macroSections`

1. Add each macro section as an object inside the `macroSections` array.
2. Provide a unique `id`, `title` and an `icon` object with a `name` field.
3. Optionally include a `description` and `order` to control display ordering.
4. Define a `sections` array containing subsection objects. Each subsection must include:
   - `id`: numeric identifier unique across all sections
   - `section_key`: key referencing its section
   - `title`: display name
   - `icon`: `{ name }` object
   - `content`: markdown or text for the section body
   - optional fields such as `phase`, `info_card_title`, `info_card_content` and `order_index`

#### Example `macroSection`

```json
"macroSections": [
  {
    "id": "introduction",
    "title": "Introduction",
    "icon": { "name": "Info" },
    "description": "High-level overview of the proposal",
    "sections": [
      {
        "id": 1,
        "section_key": "overview",
        "title": "Project Overview",
        "icon": { "name": "BookOpen" },
        "phase": "Discovery",
        "content": "## Overview\nProvide project details.",
        "info_card_title": "Goal",
        "info_card_content": "Explain the objective",
        "order_index": 1
      }
    ],
    "order": 1
  }
]
```

### Generate macro section boilerplate

Use the helper script to scaffold new macro sections and subsections:

```bash
npm run generate-macro -- --id introduction --title "Introduction"
```

The command prints a JSON template that can be copied into a lead file.

### Run the ingestion script

1. Place a populated lead file in the `leads/` directory, e.g. `leads/my-lead.json`.
2. Run the ingestion script:

   ```bash
   npm run ingest -- leads/my-lead.json
   ```

   The script validates the file and writes the processed configuration to `lib/platform-config.ts`.

## Proposal Generation (Per‑Lead Branches)

This repository is a template to generate a dedicated proposal branch per lead.

Two ways to generate proposals:

- Manual (local testing):
  - Place `leads/<slug>.json`
  - Run `npm run ingest -- leads/<slug>.json`
  - Run the app to verify the proposal content

- Automated (branch creation + push):
  - Ensure the remote has a template branch (defaults to `structured-template`).
  - Run: `bash scripts/create-proposal.sh <slug>`
    - Uses `TEMPLATE_BRANCH=structured-template` by default (override via env var)
    - Clones the template branch to `proposal-<slug>`
    - Runs `npm install` and ingests `leads/<slug>.json`
    - Creates and pushes a new branch: `proposal/<slug>` containing the generated `lib/platform-config.ts`

### CI/CD for Proposal Branches

- GitHub Actions workflow `.github/workflows/proposal-deploy.yml` deploys any push to `proposal/**`.
- Requires `VERCEL_TOKEN` secret in the GitHub repo settings.
- Once the proposal branch is pushed, the workflow builds and triggers a Vercel deployment.

### JSON Schema and Templates

- Schema: `scripts/lead-config.schema.json`
- Starter file: `leads/lead-template.json`
- For adding new macro sections programmatically:
  - `npm run generate-macro -- --id <macro-id> --title "Title" [--section-count N] [--start-id M] [--order O]`
  - Copy the printed JSON into your lead file’s `macroSections`.

## Branch Workflow

Typical branch-based development flow:

```bash
git checkout -b feature/my-change   # create a new branch
git add <files>                     # stage modifications
git commit -m "feat: my change"     # commit
git push origin feature/my-change   # push the branch
```
