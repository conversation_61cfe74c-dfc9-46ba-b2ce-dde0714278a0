import { generateText } from "ai"
import { pepSections, processedConfig } from "@/lib/platform-data"

export async function POST(req: Request) {
  try {
    console.log("[v0] Chat API called")

    const { messages, activeSection } = await req.json()
    console.log("[v0] Request data:", { messagesCount: messages.length, activeSection })

    // Get the last user message
    const lastMessage = messages[messages.length - 1]
    console.log("[v0] User message:", lastMessage.content)

    const pepContext = pepSections
      .map((section) => `Section ${section.id}: ${section.title} (${section.phase})\n${section.content}`)
      .join("\n\n---\n\n")

    // Enhanced context with project metadata and branding
    const projectContext = `
Project: ${processedConfig.metadata.title}
Company: ${processedConfig.branding.companyName}
Description: ${processedConfig.metadata.description}
Timeline: ${processedConfig.variables.timeline} weeks
Budget: ${processedConfig.variables.budget}
Expected ROI: ${processedConfig.variables.expectedROI}
Market Size: ${processedConfig.variables.marketSize}
Development Weeks: ${processedConfig.variables.developmentWeeks}
`

    const currentSectionInfo =
      activeSection && activeSection > 0
        ? `The user is currently viewing Section ${activeSection}: ${pepSections[activeSection - 1]?.title || "Unknown"}.`
        : ""

    console.log("[v0] About to call generateText with API key:", process.env.AI_GATEWAY_API_KEY ? "Present" : "Missing")

    const result = await generateText({
      model: "anthropic/claude-3-5-sonnet-20241022",
      messages: [
        {
          role: "system",
          content: `You are a knowledgeable assistant for Singular Agency's Product Enhancement Proposal (PEP) for a ${processedConfig.metadata.title} project. You have access to the complete PEP documentation with ${pepSections.length} sections covering everything from executive summary to technical specifications.

${projectContext}

${currentSectionInfo}

When users ask questions, provide detailed, accurate answers based on the PEP content. If a question relates to a specific section, you can suggest they navigate to that section by using the format: [View Section X: Title](section:X) - this will create a clickable link.

Here is the complete PEP content for reference:

${pepContext}

Guidelines:
- Provide comprehensive, accurate answers based on the PEP content
- Reference specific sections when relevant
- Use the clickable section format [View Section X: Title](section:X) when suggesting users explore specific sections
- Be helpful and professional
- If asked about Singular Agency, emphasize our expertise and proven track record
- Use the project context (timeline, budget, ROI, market size) to provide more specific and accurate information`,
        },
        {
          role: "user",
          content: lastMessage.content,
        },
      ],
    })

    console.log("[v0] generateText completed successfully")
    console.log("[v0] AI response:", result.text.substring(0, 200) + "...")

    return new Response(JSON.stringify({
      response: result.text,
    }), {
      headers: {
        'Content-Type': 'application/json',
      },
    })
  } catch (error) {
    console.error("[v0] Chat API error:", error)
    if (error instanceof Error) {
      console.error("[v0] Error details:", error.message, error.stack)
    }
    return new Response(JSON.stringify({
      response:
        `I apologize, but I'm experiencing a technical issue. Please try asking your question again. I'm here to help you understand the ${processedConfig.metadata.title} PEP document and can answer questions about any of the ${pepSections.length} sections covering business case, technical architecture, timeline, and more.`,
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }
}
