@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* Pure white for clean, professional look */
  --foreground: oklch(0.205 0 0); /* Dark gray for primary text */
  --card: oklch(0.97 0 0); /* Light gray for card backgrounds */
  --card-foreground: oklch(0.439 0 0); /* Medium gray for card text */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.205 0 0);
  --primary: oklch(0.205 0 0); /* Professional dark gray */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.65 0.25 35); /* Singular orange for highlights */
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.439 0 0);
  --accent: oklch(0.65 0.25 35); /* Singular orange for CTAs */
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.85 0 0); /* Subtle borders */
  --input: oklch(0.985 0 0);
  --ring: oklch(0.65 0.25 35);
  --chart-1: oklch(0.65 0.25 35); /* Singular orange */
  --chart-2: oklch(0.7 0.3 195); /* Singular cyan */
  --chart-3: oklch(0.25 0 0); /* Singular dark navy */
  --chart-4: oklch(0.769 0.188 70.08);
  --chart-5: oklch(0.645 0.246 16.439);
  --radius: 0.75rem; /* Slightly larger radius for modern look */
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.205 0 0);
  --sidebar-primary: oklch(0.97 0 0);
  --sidebar-primary-foreground: oklch(0.205 0 0);
  --sidebar-accent: oklch(0.65 0.25 35);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.85 0 0);
  --sidebar-ring: oklch(0.65 0.25 35);
  --font-geist-sans: "Geist Sans", system-ui, sans-serif;
  --font-manrope: "Manrope", Georgia, serif;
  --singular-orange: oklch(0.65 0.25 35);
  --singular-cyan: oklch(0.7 0.3 195);
  --singular-navy: oklch(0.25 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.65 0.25 35);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.65 0.25 35);
  --accent-foreground: oklch(1 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.65 0.25 35);
  --chart-1: oklch(0.65 0.25 35);
  --chart-2: oklch(0.7 0.3 195);
  --chart-3: oklch(0.25 0 0);
  --chart-4: oklch(0.769 0.188 70.08);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.269 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.65 0.25 35);
  --sidebar-accent-foreground: oklch(1 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.65 0.25 35);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Glass morphism effects */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Smooth animations */
* {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.5);
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-serif: var(--font-manrope);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

/* Premium gradient effects */
.gradient-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dark .gradient-hero {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

/* Singular brand gradient classes */
.gradient-singular {
  background: linear-gradient(135deg, oklch(0.65 0.25 35) 0%, oklch(0.7 0.3 195) 100%);
}

.gradient-singular-subtle {
  background: linear-gradient(135deg, oklch(0.98 0.05 35) 0%, oklch(0.98 0.05 195) 100%);
}

/* PostHog Session Recording Support */
.posthog-session-recording {
  background-color: var(--background);
  color: var(--foreground);
}

/* Ensure PostHog can capture theme colors properly */
html[data-theme="light"] .posthog-session-recording {
  background-color: oklch(1 0 0);
  color: oklch(0.205 0 0);
}

html[data-theme="dark"] .posthog-session-recording {
  background-color: oklch(0.145 0 0);
  color: oklch(0.985 0 0);
}
