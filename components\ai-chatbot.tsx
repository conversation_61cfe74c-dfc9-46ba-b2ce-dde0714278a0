"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import { track } from "@vercel/analytics"
import { analytics } from "@/lib/analytics"
import { Send, Bot, User, ChevronRight, Copy, ThumbsUp, ThumbsDown, Sparkles, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  reactions?: { type: "like" | "dislike"; count: number }[]
}

interface AiChatbotProps {
  activeSection: number
  onSectionChange?: (sectionId: number) => void
}

const MarkdownRenderer = ({
  content,
  onSectionChange,
}: { content: string; onSectionChange?: (sectionId: number) => void }) => {
  const parseMarkdown = (text: string) => {
    // Parse section references like [View Section X: Title](section:X) or [View Section X: Title](X)
    const sectionReferenceRegex = /\[View Section (\d+): ([^\]]+)\]\(section?:(\d+)\)/g

    let parsedText = text
    let match
    const sectionLinks: Array<{ fullMatch: string; sectionId: number; title: string }> = []

    // Extract all section references
    while ((match = sectionReferenceRegex.exec(text)) !== null) {
      const [fullMatch, _, title, sectionId] = match
      sectionLinks.push({ fullMatch, sectionId: Number.parseInt(sectionId), title })
    }

    sectionLinks.forEach(({ fullMatch, sectionId, title }) => {
      if (onSectionChange) {
        // Escape the title to prevent syntax errors
        const escapedTitle = title
          .replace(/'/g, "&#39;")
          .replace(/"/g, "&quot;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
        const clickableLink = `<button class="inline-flex items-center gap-1 px-3 py-2 bg-accent/10 hover:bg-accent/20 text-accent border border-accent/30 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 cursor-pointer hover:shadow-md" title="Click to navigate to ${sectionId}" data-section-id="${sectionId}" data-section-title="${escapedTitle}">📄 ${escapedTitle}</button>`
        parsedText = parsedText.replace(fullMatch, clickableLink)
      } else {
        // Fallback if no navigation function
        const escapedTitle = title
          .replace(/'/g, "&#39;")
          .replace(/"/g, "&quot;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;")
        const fallbackLink = `<span class="inline-flex items-center gap-1 px-3 py-2 bg-accent/10 text-accent border border-accent/30 rounded-lg text-sm font-medium">📄 ${escapedTitle}</span>`
        parsedText = parsedText.replace(fullMatch, fallbackLink)
      }
    })

    // Parse regular markdown
    return parsedText
      .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-foreground">$1</strong>')
      .replace(/\*(.*?)\*/g, '<em class="italic text-foreground/90">$1</em>')
      .replace(
        /`(.*?)`/g,
        '<code class="bg-muted/60 px-2 py-1 rounded-md text-sm font-mono text-accent-foreground border border-border/30">$1</code>',
      )
      .replace(/^### (.*$)/gim, '<h3 class="text-base font-semibold mt-4 mb-2 text-foreground leading-tight">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-lg font-semibold mt-5 mb-3 text-foreground leading-tight">$2</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-xl font-bold mt-6 mb-3 text-foreground leading-tight">$1</h1>')
      .replace(/^- (.*$)/gim, '<li class="ml-4 mb-1 text-sm leading-relaxed list-disc marker:text-accent">$1</li>')
      .replace(
        /^\d+\. (.*$)/gim,
        '<li class="ml-4 mb-1 text-sm leading-relaxed list-decimal marker:text-accent marker:font-medium">$1</li>',
      )
      .replace(/\n\n/g, '</p><p class="mb-3 text-sm leading-relaxed text-foreground/95">')
  }

  useEffect(() => {
    const handleSectionButtonClick = (event: Event) => {
      const target = event.target as HTMLElement
      if (target.tagName === "BUTTON" && target.hasAttribute("data-section-id")) {
        const sectionId = Number.parseInt(target.getAttribute("data-section-id") || "0")
        const sectionTitle = target.getAttribute("data-section-title") || ""

        if (onSectionChange && sectionId) {
          // Track the section reference click
          analytics.trackChatbotSectionReference(
            sectionId,
            sectionTitle,
            0, // We don't have activeSection context here, but we can track the click
          )

          // Track the navigation action
          analytics.trackButtonClick(`Navigate to ${sectionTitle}`, "chatbot_section_reference", sectionId)

          onSectionChange(sectionId)
        }
      }
    }

    document.addEventListener("click", handleSectionButtonClick)

    return () => {
      document.removeEventListener("click", handleSectionButtonClick)
    }
  }, [onSectionChange])

  return (
    <div
      className="prose prose-sm max-w-none text-inherit [&>p]:text-sm [&>p]:leading-relaxed [&>p]:text-foreground/95"
      dangerouslySetInnerHTML={{
        __html: `<p class="mb-3 text-sm leading-relaxed text-foreground/95">${parseMarkdown(content)}</p>`,
      }}
    />
  )
}

export function AiChatbot({ activeSection, onSectionChange }: AiChatbotProps) {
  const [isCollapsed, setIsCollapsed] = useState(true) // Start collapsed by default to show the improved bubble
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [currentSectionQuestions, setCurrentSectionQuestions] = useState<string[]>([])
  const [isTyping, setIsTyping] = useState(false)
  const [isQuickQuestionsCollapsed, setIsQuickQuestionsCollapsed] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (activeSection > 0) {
      const questions = generateSectionQuestions(activeSection)
      setCurrentSectionQuestions(questions)
    } else {
      setCurrentSectionQuestions([
        "What's the overall project vision?",
        "How does Singular Agency ensure success?",
        "What's the expected timeline and investment?",
        "Why choose Singular Agency for this project?",
      ])
    }
  }, [activeSection])

  const generateSectionQuestions = (section: number): string[] => {
    const baseQuestions = [`What's included in this section?`, `How does this relate to the overall project?`]

    if (section <= 3) {
      return [...baseQuestions, "How does this impact the business strategy?", "What are the key success factors?"]
    } else {
      return [...baseQuestions, "What technical decisions are involved?", "How will this be implemented?"]
    }
  }

     const handleSubmit = async (e: React.FormEvent) => {
     e.preventDefault()
     if (!input.trim() || isLoading) return

     // Collapse quick questions when custom input is submitted
     setIsQuickQuestionsCollapsed(true)

     const startTime = Date.now()
     const question = input.trim()

     // Track with both Vercel and PostHog
     track("chatbot_prompt_submitted", {
       promptLength: question.length,
       activeSection,
       promptType: "manual_input",
       timestamp: new Date().toISOString(),
     })

     analytics.trackChatbotQuestion(question, "manual_input", activeSection)

     const userMessage: Message = {
       id: Date.now().toString(),
       role: "user",
       content: question,
       timestamp: new Date(),
     }

     setMessages((prev) => [...prev, userMessage])
     setInput("")
     setIsLoading(true)
     setIsTyping(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 800))

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          activeSection,
        }),
      })

      if (!response.ok) throw new Error("Failed to get response")

      const data = await response.json()
      const responseTime = Date.now() - startTime

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.response || "I'm here to help you with the PEP document. What would you like to know?",
        timestamp: new Date(),
      }

      // Track chatbot response
      analytics.trackChatbotResponse(question, data.response?.length || 0, activeSection, responseTime)

      // Track section references if they exist in the response
      const sectionReferenceRegex = /\[View Section (\d+): ([^\]]+)\]\(section?:(\d+)\)/g
      let match
      while ((match = sectionReferenceRegex.exec(data.response)) !== null) {
        const [_, __, title, sectionId] = match
        analytics.trackChatbotSectionReference(Number.parseInt(sectionId), title, activeSection)
      }

      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      console.error("Chat error:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content:
          "I'm here to help you with the PEP document. What would you like to know about the Social Network Marketplace project?",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      setIsTyping(false)
      inputRef.current?.focus()
    }
  }

  const handleSuggestedQuestion = async (question: string) => {
    if (isLoading) return

    setIsQuickQuestionsCollapsed(true)

    const startTime = Date.now()

    // Track with both Vercel and PostHog
    track("chatbot_suggested_question_clicked", {
      question,
      activeSection,
      promptType: "suggested_question",
      timestamp: new Date().toISOString(),
    })

    analytics.trackChatbotQuestion(question, "suggested_question", activeSection)

    const userMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: question,
      timestamp: new Date(),
    }

    setMessages((prev) => [...prev, userMessage])
    setIsLoading(true)
    setIsTyping(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 800))

      const response = await fetch("/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          messages: [...messages, userMessage],
          activeSection,
        }),
      })

      if (!response.ok) throw new Error("Failed to get response")

      const data = await response.json()
      const responseTime = Date.now() - startTime

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content: data.response || "I'm here to help you with the PEP document. What would you like to know?",
        timestamp: new Date(),
      }

      // Track chatbot response
      analytics.trackChatbotResponse(question, data.response?.length || 0, activeSection, responseTime)

      // Track section references if they exist in the response
      const sectionReferenceRegex = /\[View Section (\d+): ([^\]]+)\]\(section?:(\d+)\)/g
      let match
      while ((match = sectionReferenceRegex.exec(data.response)) !== null) {
        const [_, __, title, sectionId] = match
        analytics.trackChatbotSectionReference(Number.parseInt(sectionId), title, activeSection)
      }

      setMessages((prev) => [...prev, assistantMessage])
    } catch (error) {
      console.error("Chat error:", error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: "assistant",
        content:
          "I'm here to help you with the PEP document. What would you like to know about the Social Network Marketplace project?",
        timestamp: new Date(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
      setIsTyping(false)
      inputRef.current?.focus()
    }
  }

  const handleReaction = (messageId: string, reactionType: "like" | "dislike") => {
    track("chatbot_message_reaction", {
      messageId,
      reactionType,
      activeSection,
      timestamp: new Date().toISOString(),
    })

    setMessages((prev) =>
      prev.map((msg) => {
        if (msg.id === messageId) {
          const reactions = msg.reactions || []
          const existingReaction = reactions.find((r) => r.type === reactionType)

          if (existingReaction) {
            existingReaction.count += 1
          } else {
            reactions.push({ type: reactionType, count: 1 })
          }

          return { ...msg, reactions }
        }
        return msg
      }),
    )
  }

  const handleCopyMessage = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content)
      track("chatbot_message_copied", {
        contentLength: content.length,
        activeSection,
        timestamp: new Date().toISOString(),
      })
    } catch (err) {
      console.error("Failed to copy message:", err)
    }
  }

  return (
    <div
      className={cn(
        "max-h-[80vh] bg-background/95 backdrop-blur-xl border border-border/50 rounded-2xl transition-all duration-500 ease-out flex flex-col shadow-2xl",
        isCollapsed ? "w-16 h-16" : "w-96", // Fixed height for collapsed state
      )}
    >
      {isCollapsed ? (
        <div className="w-full h-full flex items-center justify-center relative">
                     {/* Main message icon with conditional blinking when collapsed */}
           <button
             onClick={() => setIsCollapsed(false)}
             className="relative z-10 w-12 h-12 rounded-xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center shadow-lg ring-1 ring-accent/20 hover:scale-110 transition-all duration-300 group animate-pulse"
           >
             <MessageCircle className="h-6 w-6 text-white" />

             {/* Notification dot - static green dot */}
             <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full shadow-sm"></div>

             {/* Hover wave effect */}
             <div className="absolute inset-0 rounded-xl bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
           </button>
        </div>
      ) : (
        <>
          <div className="p-4 border-b border-border/30 bg-gradient-to-r from-background/80 to-muted/20 backdrop-blur-sm flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="relative">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center shadow-lg ring-1 ring-accent/20">
                  <Sparkles className="h-5 w-5 text-white" />
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-base text-foreground flex items-center gap-2 leading-tight">
                  PEP Assistant
                  <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
                </h3>
                <p className="text-xs text-muted-foreground font-medium leading-tight">
                  {activeSection === 0 ? "Home Overview" : `Section ${activeSection} Analysis`}
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 active:scale-95"
            >
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>

          <div className="flex-1 overflow-y-auto p-4 space-y-4 scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
            {messages.length === 0 && (
              <div className="text-center text-muted-foreground py-8 px-2">
                <div className="relative mx-auto mb-4">
                  <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center shadow-lg ring-1 ring-accent/20">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                </div>
                <p className="font-semibold text-base text-foreground mb-2 leading-tight">
                  Hi! I'm your PEP Assistant.
                </p>
                <p className="mb-3 leading-relaxed text-sm text-muted-foreground">
                  {activeSection === 0
                    ? "Ask me about the overall project or use the quick questions below."
                    : `Ask me about Section ${activeSection} or use the quick questions below.`}
                </p>
                <p className="text-xs opacity-75 font-medium text-muted-foreground/80">
                  Powered by Singular Agency's expertise in delivering scalable digital solutions.
                </p>
              </div>
            )}

            {messages.map((message, index) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3 group animate-in slide-in-from-bottom-2 duration-300",
                  message.role === "user" ? "justify-end" : "justify-start",
                )}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {message.role === "assistant" && (
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center flex-shrink-0 shadow-sm ring-1 ring-accent/20">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                )}

                <div className="flex flex-col max-w-[85%]">
                  <div
                    className={cn(
                      "p-4 rounded-2xl shadow-sm border transition-all duration-200 hover:shadow-md",
                      message.role === "user"
                        ? "bg-accent text-white border-accent/20 rounded-br-md shadow-accent/10"
                        : "bg-card/80 backdrop-blur-sm text-card-foreground border-border/30 rounded-bl-md",
                    )}
                  >
                    {message.role === "assistant" ? (
                      <MarkdownRenderer content={message.content} onSectionChange={onSectionChange} />
                    ) : (
                      <p className="text-sm leading-relaxed font-medium">{message.content}</p>
                    )}
                  </div>

                  <div
                    className={cn(
                      "flex items-center gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200",
                      message.role === "user" ? "justify-end" : "justify-start",
                    )}
                  >
                    <span className="text-xs text-muted-foreground font-medium">
                      {message.timestamp.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </span>

                    {message.role === "assistant" && (
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => handleCopyMessage(message.content)}
                          className="p-1.5 rounded-md hover:bg-muted/50 transition-colors active:scale-95"
                          title="Copy message"
                        >
                          <Copy className="h-3 w-3 text-muted-foreground" />
                        </button>
                        <button
                          onClick={() => handleReaction(message.id, "like")}
                          className="p-1.5 rounded-md hover:bg-muted/50 transition-colors active:scale-95"
                          title="Like message"
                        >
                          <ThumbsUp className="h-3 w-3 text-muted-foreground" />
                        </button>
                        <button
                          onClick={() => handleReaction(message.id, "dislike")}
                          className="p-1.5 rounded-md hover:bg-muted/50 transition-colors active:scale-95"
                          title="Dislike message"
                        >
                          <ThumbsDown className="h-3 w-3 text-muted-foreground" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {message.role === "user" && (
                  <div className="w-8 h-8 rounded-xl bg-muted/80 backdrop-blur-sm flex items-center justify-center flex-shrink-0 shadow-sm border border-border/30">
                    <User className="h-4 w-4 text-muted-foreground" />
                  </div>
                )}
              </div>
            ))}

            {isTyping && (
              <div className="flex gap-3 justify-start animate-in slide-in-from-bottom-2 duration-300">
                <div className="relative">
                  <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-accent to-accent/80 flex items-center justify-center flex-shrink-0 shadow-sm ring-1 ring-accent/20">
                    <Bot className="h-4 w-4 text-white" />
                  </div>
                </div>
                <div className="bg-card/80 backdrop-blur-sm border border-border/30 p-4 rounded-2xl rounded-bl-md shadow-sm">
                  <div className="flex items-center space-x-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-accent rounded-full shadow-sm"></div>
                      <div
                        className="w-2 h-2 bg-accent rounded-full shadow-sm"
                        style={{ animationDelay: "0.1s" }}
                      ></div>
                      <div
                        className="w-2 h-2 bg-accent rounded-full shadow-sm"
                        style={{ animationDelay: "0.2s" }}
                      ></div>
                    </div>
                    <span className="text-xs text-muted-foreground font-medium">Assistant is typing...</span>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          <div className="p-4 border-t border-border/30 bg-gradient-to-r from-background/80 to-muted/10 backdrop-blur-sm">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-semibold text-foreground flex items-center gap-2 leading-tight">
                  <Sparkles className="h-4 w-4 text-accent" />
                  Quick Questions
                </h4>
                <button
                  onClick={() => setIsQuickQuestionsCollapsed(!isQuickQuestionsCollapsed)}
                  className="p-1.5 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 active:scale-95"
                  title={isQuickQuestionsCollapsed ? "Expand Quick Questions" : "Collapse Quick Questions"}
                >
                  <ChevronRight
                    className={cn(
                      "h-4 w-4 text-muted-foreground transition-transform duration-200",
                      isQuickQuestionsCollapsed ? "rotate-0" : "rotate-90",
                    )}
                  />
                </button>
              </div>

              <div
                className={cn(
                  "grid grid-cols-1 gap-2 transition-all duration-300 ease-out overflow-hidden",
                  isQuickQuestionsCollapsed ? "max-h-0 opacity-0" : "max-h-96 opacity-100",
                )}
              >
                {currentSectionQuestions.map((question, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestedQuestion(question)}
                    disabled={isLoading}
                    className="w-full text-left p-3 text-xs font-medium rounded-xl bg-card/60 hover:bg-accent/10 text-card-foreground transition-all duration-200 hover:scale-[1.02] hover:shadow-sm border border-border/30 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm leading-relaxed"
                  >
                    {question}
                  </button>
                ))}
              </div>
            </div>

            <form onSubmit={handleSubmit} className="flex gap-3">
              <div className="flex-1 relative">
                <Input
                  ref={inputRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="Ask about this section..."
                  className="bg-input/80 backdrop-blur-sm border-border/30 focus:border-accent focus:ring-accent/20 rounded-xl pr-12 transition-all duration-200 text-sm font-medium placeholder:text-muted-foreground/70"
                  disabled={isLoading}
                />
                {input && (
                  <button
                    type="button"
                    onClick={() => setInput("")}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors text-lg leading-none"
                  >
                    ×
                  </button>
                )}
              </div>
              <Button
                type="submit"
                size="sm"
                disabled={isLoading || !input.trim()}
                className="bg-accent hover:bg-accent/90 text-white rounded-xl px-4 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 disabled:hover:scale-100 font-medium"
              >
                <Send className="h-4 w-4" />
              </Button>
            </form>

            <p className="text-xs text-muted-foreground/80 mt-3 text-center font-medium">
              Press Enter to send • Powered by Singular Agency
            </p>
          </div>
        </>
      )}
    </div>
  )
}
