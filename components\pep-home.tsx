"use client"

import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { analytics } from "@/lib/analytics"
import { content, branding } from "@/lib/platform-data"
import {
  Users,
  ArrowRight,
  ExternalLink,
  Target,
  TrendingUp,
  Clock,
  Lightbulb,
  Wrench,
  Rocket,
  RefreshCw,
  HeartHandshake,
  Star,
  Award,
    MessageSquare,
    ShoppingCart,
    Camera,
    Smartphone,
  } from "lucide-react"

// Icon mapping for dynamic icons
const iconMap: Record<string, any> = {
  Users,
  Target,
  TrendingUp,
  Clock,
  Lightbulb,
  Wrench,
  Rocket,
  RefreshCw,
  HeartHandshake,
  Star,
  Award,
  MessageSquare,
  ShoppingCart,
  Camera,
  Smartphone,
}

interface PepHomeProps {
  onSectionChange: (sectionId: number) => void
}

export function PepHome({ onSectionChange }: PepHomeProps) {
  const handleExploreMethodology = () => {
    analytics.trackButtonClick("Explore Methodology", "hero_section", 0)
    // Scroll to methodology section on the same page
    const methodologySection = document.querySelector('[data-section="methodology"]')
    if (methodologySection) {
      methodologySection.scrollIntoView({ behavior: "smooth" })
    } else {
      // Fallback: scroll to methodology section by class or approximate position
      const methodologyElement =
        document.querySelector(".methodology-section") || document.querySelector('[class*="methodology"]')
      if (methodologyElement) {
        methodologyElement.scrollIntoView({ behavior: "smooth" })
      } else {
        // Final fallback: scroll to approximate position
        window.scrollTo({ top: window.innerHeight * 2, behavior: "smooth" })
      }
    }
  }

  const handleViewProjectSummary = () => {
    analytics.trackButtonClick("View Project Summary", "hero_section", 0)
    onSectionChange(1) // Navigate to Executive Summary section
  }

  const handleGetProposal = () => {
    analytics.trackButtonClick("Get Proposal", "header", 0)
    if (content.proposalUrl) {
      window.open(content.proposalUrl, "_blank")
    }
  }

  const handleWhySingularButton = () => {
    analytics.trackButtonClick("Explore Methodology", "why_singular_section", 0)
    onSectionChange(1)
  }

  const handleReadyToTransform = () => {
    analytics.trackButtonClick("Get Your Proposal", "cta_section", 0)
    if (content.proposalUrl) {
      window.open(content.proposalUrl, "_blank")
    }
  }

  const heroTitle = content.hero.title || ""
  const [heroHighlight, heroRest] = heroTitle.split(":")

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="gradient-hero min-h-screen flex flex-col">
        {/* Header */}
        <div className="px-8 py-6">
          <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-3">
              <img
                src={branding.logo || "/placeholder.svg"}
                alt={branding.companyName}
                className="w-12 h-12 object-contain"
              />
              <div>
                <h1 className="text-xl font-bold text-foreground font-sans">{branding.companyName}</h1>
                <p className="text-sm text-muted-foreground">{branding.companyDescription}</p>
                <p className="text-xs text-muted-foreground">for {branding.companyName}</p>
              </div>
            </div>
            <Button
              onClick={handleGetProposal}
              className="bg-accent hover:bg-accent/90 text-accent-foreground font-medium"
            >
              Get Proposal <ExternalLink className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>

        {/* Hero Content */}
        <div className="flex-1 flex items-center px-8">
          <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-5xl lg:text-6xl font-bold text-foreground mb-6 font-sans leading-tight">
                {heroHighlight && (
                  <span className="text-7xl" style={{ color: branding.accentColor }}>
                    {heroHighlight}:
                  </span>
                )}{" "}
                <span className="text-5xl">{heroRest ? heroRest.trim() : heroTitle}</span>
              </h1>
              {content.hero.subtitle && (
                <p className="text-2xl text-muted-foreground mb-4 font-sans">
                  {content.hero.subtitle}
                </p>
              )}
              <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">
                {content.hero.description}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  onClick={handleExploreMethodology}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-8"
                >
                  {content.hero.primaryCTA} <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleViewProjectSummary}
                  className="border-border hover:bg-muted font-medium px-8 bg-transparent"
                >
                  {content.hero.secondaryCTA}
                </Button>
              </div>
            </div>

            <div className="relative">
              <Card className="glass p-8 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent" />
                <div className="relative z-10">
                  <h3 className="text-2xl font-bold text-foreground mb-6 font-sans">Platform Architecture</h3>

                  {/* Interactive Platform Diagram */}
                  <div className="space-y-6">
                    <div className="flex items-center justify-center">
                      <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center border-2 border-accent/30">
                        <Users className="h-8 w-8 text-accent" />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      {(content.hero.features || []).slice(0, 4).map((feature, index) => {
                        const icons = [MessageSquare, ShoppingCart, Camera, Smartphone]
                        const colors = ["chart-1", "chart-2", "chart-3", "chart-4"]
                        const Icon = icons[index]

                        return (
                          <div
                            key={index}
                            className="flex flex-col items-center p-4 bg-card rounded-lg border border-border/50"
                          >
                            <Icon className={`h-6 w-6 text-${colors[index]} mb-2`} />
                            <span className="text-sm font-medium text-card-foreground text-center">{feature}</span>
                          </div>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <div className="px-8 py-16 bg-background">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-serif">
              {content.methodology.description}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-16">
            {(content.statistics || []).map((stat, index) => {
              const Icon = iconMap[stat.icon.name] || Award
              return (
                <Card
                  key={stat.id}
                  className="p-6 text-center hover:shadow-lg transition-all duration-300 border border-border/50"
                >
                  <Icon className="h-8 w-8 mx-auto mb-3" style={{ color: stat.color }} />
                  <div className="text-3xl font-bold text-foreground mb-1 font-sans">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </Card>
              )
            })}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="p-8 hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center">
                  <Target className="h-6 w-6 text-accent" />
                </div>
                <div>
                  <h3 className="font-bold text-foreground font-sans">Proven Methodology</h3>
                  <p className="text-sm text-muted-foreground">5-Phase Excellence</p>
                </div>
              </div>
              <p className="text-muted-foreground font-serif">
                Our systematic approach ensures predictable outcomes, reduced risk, and exceptional results that
                consistently exceed client expectations.
              </p>
            </Card>

            <Card className="p-8 hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-chart-1/10 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-chart-1" />
                </div>
                <div>
                  <h3 className="font-bold text-foreground font-sans">Expert Team</h3>
                  <p className="text-sm text-muted-foreground">Senior Specialists</p>
                </div>
              </div>
              <p className="text-muted-foreground font-serif">
                Senior developers, architects, and strategists with deep expertise in scalable platform development and
                cutting-edge technologies.
              </p>
            </Card>

            <Card className="p-8 hover:shadow-lg transition-all duration-300 border border-border/50">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-chart-4/10 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-chart-4" />
                </div>
                <div>
                  <h3 className="font-bold text-foreground font-sans">Business Focus</h3>
                  <p className="text-sm text-muted-foreground">Revenue Driven</p>
                </div>
              </div>
              <p className="text-muted-foreground font-serif">
                We build solutions that drive measurable business value from day one, not just impressive technology
                demonstrations.
              </p>
            </Card>
          </div>
        </div>
      </div>

      <div className="px-8 py-16 bg-muted/30">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16" data-section="methodology">
            <Badge variant="outline" className="mb-4">
              Our Methodology
            </Badge>
            <h2 className="text-4xl font-bold text-foreground mb-6 font-sans">
              {content.methodology?.title || "Our Proven Development Methodology"}
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto font-serif">
              {content.methodology.description}
            </p>
          </div>

        </div>
      </div>

      <div className="px-8 py-16 bg-background">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold mb-6 font-sans">{content.cta.title}</h2>
          <p className="text-xl mb-8 opacity-90 font-serif">{content.cta.description}</p>
          <div className="flex justify-center">
            <Button
              size="lg"
              variant="secondary"
              onClick={handleReadyToTransform}
              className="bg-accent hover:bg-accent/90 text-accent-foreground font-medium px-8"
            >
              {content.cta.buttonText} <ExternalLink className="h-5 w-5 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
