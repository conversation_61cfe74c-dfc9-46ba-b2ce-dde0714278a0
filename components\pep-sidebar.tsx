"use client"

import { useState } from "react"
import { track } from "@vercel/analytics"
import { analytics } from "@/lib/analytics"
import { getMacroSectionForSection, macroSections, pepSections, branding } from "@/lib/platform-data"
import {
  ChevronRight,
  ChevronDown,
  FileText,
  TrendingUp,
  Target,
  Layers,
  Shield,
  Database,
  Code,
  Zap,
  BarChart3,
  Users,
  DollarSign,
  Calendar,
  CheckSquare,
  AlertTriangle,
  Settings,
  BookOpen,
} from "lucide-react"
import { cn } from "@/lib/utils"

// Icon mapping for dynamic icons
const iconMap: Record<string, any> = {
  FileText,
  TrendingUp,
  Target,
  Layers,
  Shield,
  Database,
  Code,
  Zap,
  BarChart3,
  Users,
  DollarSign,
  Calendar,
  CheckSquare,
  AlertTriangle,
  Settings,
  BookOpen,
}

interface PepSidebarProps {
  activeSection: number
  onSectionChange: (sectionId: number) => void
}

export function PepSidebar({ activeSection, onSectionChange }: PepSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [expandedSections, setExpandedSections] = useState<string[]>(["overview"])

  const toggleMacroSection = (macroId: string) => {
    const action = expandedSections.includes(macroId) ? "collapse" : "expand"

    // Track with both Vercel and PostHog
    track("sidebar_macro_section_toggle", {
      macroId,
      action,
    })

    analytics.trackSidebarToggle(macroId, action)

    setExpandedSections((prev) => (prev.includes(macroId) ? prev.filter((id) => id !== macroId) : [...prev, macroId]))
  }

  const getActiveMacroSection = () => {
    return macroSections.find((macro) => macro.sections.some((section) => section.id === activeSection))?.id
  }

  const handleSectionChange = (sectionId: number) => {
    const sectionInfo =
      sectionId === 0
        ? { title: "Home", macroSection: "home" }
        : {
            title: pepSections.find((s) => s.id === sectionId)?.title || "Unknown",
            macroSection: getMacroSectionForSection(sectionId),
          }

    const navigationType =
      sectionId === 0
        ? "direct"
        : Math.abs(sectionId - activeSection) === 1
          ? sectionId > activeSection
            ? "next"
            : "previous"
          : "direct"

    // Track with both Vercel and PostHog
    track("sidebar_section_click", {
      sectionId,
      sectionTitle: sectionInfo.title,
      macroSection: sectionInfo.macroSection,
      previousSection: activeSection,
    })

    analytics.trackSidebarSectionClick(sectionId, sectionInfo.title, sectionInfo.macroSection)

    // Get macro sections for navigation tracking
    const currentMacroSection = getMacroSectionForSection(activeSection)
    const targetMacroSection = getMacroSectionForSection(sectionId)

    analytics.trackSectionNavigation(activeSection, sectionId, navigationType, currentMacroSection, targetMacroSection)

    onSectionChange(sectionId)
  }

  return (
    <div
      className={cn(
        "h-screen bg-white dark:bg-gray-950 border-r border-gray-200 dark:border-gray-800 transition-all duration-300",
        isCollapsed ? "w-16" : "w-80",
      )}
    >
      <div className="p-6 border-b border-gray-200 dark:border-gray-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-3">
              <img
                src={branding.logo || "/placeholder.svg"}
                alt={branding.companyName}
                className="w-8 h-8 object-contain"
              />
              <div>
                <h1 className="text-xl font-bold text-gray-900 dark:text-white">{branding.companyDescription}</h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{branding.companyName}</p>
                <p className="text-xs text-gray-500 dark:text-gray-500">for {branding.companyName}</p>
              </div>
            </div>
          )}
          {isCollapsed && (
            <img
              src={branding.logo || "/placeholder.svg"}
              alt={branding.companyName}
              className="w-8 h-8 object-contain mx-auto"
            />
          )}
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <ChevronRight
                className={cn(
                  "h-4 w-4 transition-transform text-gray-600 dark:text-gray-400",
                  isCollapsed ? "rotate-0" : "rotate-180",
                )}
              />
            </button>
          </div>
        </div>
      </div>

      <div className="overflow-y-auto h-[calc(100vh-88px)] p-4">
        <nav className="space-y-2">
          <button
            onClick={() => handleSectionChange(0)}
            className={cn(
              "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 group mb-4",
              activeSection === 0
                ? "bg-accent text-accent-foreground"
                : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300",
            )}
          >
            <FileText
              className={cn(
                "h-5 w-5 flex-shrink-0",
                activeSection === 0 ? "text-accent-foreground" : "text-gray-600 dark:text-gray-400",
              )}
            />
            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <div className="text-sm font-semibold">Home</div>
                <div className="text-xs text-gray-500 dark:text-gray-500">Overview & Diagram</div>
              </div>
            )}
            {activeSection === 0 && <div className="w-2 h-2 rounded-full bg-accent-foreground" />}
          </button>

          {macroSections.map((macro) => {
            const MacroIcon = iconMap[macro.icon.name] || FileText
            const isExpanded = expandedSections.includes(macro.id)
            const isActiveMacro = getActiveMacroSection() === macro.id

            return (
              <div key={macro.id} className="space-y-1">
                {/* Macro Section Header */}
                <button
                  onClick={() => toggleMacroSection(macro.id)}
                  className={cn(
                    "w-full flex items-center gap-3 p-3 rounded-lg text-left transition-all duration-200 group",
                    isActiveMacro
                      ? "bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white"
                      : "hover:bg-gray-50 dark:hover:bg-gray-900 text-gray-700 dark:text-gray-300",
                  )}
                >
                  <MacroIcon
                    className={cn(
                      "h-5 w-5 flex-shrink-0",
                      isActiveMacro ? "text-gray-900 dark:text-white" : "text-gray-600 dark:text-gray-400",
                    )}
                  />
                  {!isCollapsed && (
                    <>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-semibold">{macro.title}</div>
                        <div className="text-xs text-gray-500 dark:text-gray-500">{macro.sections.length} sections</div>
                      </div>
                      <ChevronDown
                        className={cn("h-4 w-4 transition-transform", isExpanded ? "rotate-0" : "-rotate-90")}
                      />
                    </>
                  )}
                </button>

                {/* Subsections */}
                {!isCollapsed && isExpanded && (
                  <div className="ml-4 space-y-1 border-l border-gray-200 dark:border-gray-700 pl-4">
                    {macro.sections.map((section) => {
                      const Icon = iconMap[section.icon.name] || FileText
                      const isActive = activeSection === section.id

                      return (
                        <button
                          key={section.id}
                          onClick={() => handleSectionChange(section.id)}
                          className={cn(
                            "w-full flex items-center gap-3 p-2 rounded-lg text-left transition-all duration-200 group",
                            isActive
                              ? "bg-accent text-accent-foreground"
                              : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-600 dark:text-gray-400",
                          )}
                        >
                          <Icon
                            className={cn(
                              "h-4 w-4 flex-shrink-0",
                              isActive ? "text-accent-foreground" : "text-gray-500 dark:text-gray-500",
                            )}
                          />
                          <div className="flex-1 min-w-0">
                            <div
                              className={cn(
                                "text-sm font-medium truncate",
                                isActive
                                  ? "text-accent-foreground"
                                  : "text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white",
                              )}
                            >
                              {section.title}
                            </div>
                          </div>
                          {isActive && <div className="w-2 h-2 rounded-full bg-accent-foreground" />}
                        </button>
                      )
                    })}
                  </div>
                )}
              </div>
            )
          })}
        </nav>
      </div>
    </div>
  )
}
