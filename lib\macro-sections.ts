// Macro section definitions and utilities for PEP Portal
export interface MacroSection {
  id: string
  title: string
  sections: number[]
}

export const macroSections: MacroSection[] = [
  {
    id: "overview",
    title: "Project Overview",
    sections: [1, 2, 3, 4]
  },
  {
    id: "requirements", 
    title: "Requirements & Design",
    sections: [5, 6, 7, 8, 9]
  },
  {
    id: "technical",
    title: "Technical Implementation",
    sections: [10, 11, 12, 13, 14]
  },
  {
    id: "business",
    title: "Business & Operations",
    sections: [15, 16, 17, 18]
  },
  {
    id: "governance",
    title: "Quality & Governance",
    sections: [19, 20, 21, 22, 23]
  }
]

// Helper function to find macro section for a given section ID
export const getMacroSectionForSection = (sectionId: number): string => {
  const macroSection = macroSections.find(macro => 
    macro.sections.includes(sectionId)
  )
  
  return macroSection ? macroSection.id : "unknown"
}

// Helper function to get macro section title for a given section ID
export const getMacroSectionTitleForSection = (sectionId: number): string => {
  const macroSection = macroSections.find(macro => 
    macro.sections.includes(sectionId)
  )
  
  return macroSection ? macroSection.title : "Unknown"
}

// Helper function to get all sections for a macro section
export const getSectionsForMacroSection = (macroSectionId: string): number[] => {
  const macroSection = macroSections.find(macro => macro.id === macroSectionId)
  return macroSection ? macroSection.sections : []
}
