{"branding": {"logo": "/images/singular-logo.png", "accentColor": "#FF6A00", "companyName": "Acme Industries", "companyDescription": "Industrial IoT and automation solutions"}, "variables": {"projectName": "Acme Smart Sensor Dashboard", "timeline": "6", "budget": "$85,000", "expectedROI": "2.4x", "marketSize": "$5.6B", "developmentWeeks": "6"}, "content": {"proposalUrl": "https://example.com/proposals/acme-2025", "fallbackObjective": "Validate that the selected stack can process high‑volume sensor telemetry with viable unit economics and a maintainable path to scale.", "hero": {"title": "Acme: Real‑Time Sensor Intelligence", "subtitle": "From raw telemetry to business insight", "badge": "Phase 1 — POC Migration", "description": "A web platform that aggregates device telemetry, flags anomalies, and provides actionable dashboards to operations teams.", "primaryCTA": "Explore Our Methodology", "secondaryCTA": "View Project Summary", "features": ["Real‑time streams", "Anomaly detection", "Role‑based access", "Exports & alerts"]}, "statistics": [{"id": 1, "value": "250+", "label": "Active Devices", "icon": {"name": "Cpu"}, "color": "#3b82f6"}, {"id": 2, "value": "1.2M", "label": "Daily Events", "icon": {"name": "Activity"}, "color": "#10b981"}, {"id": 3, "value": "<200ms", "label": "Alert Latency", "icon": {"name": "Bell"}, "color": "#f59e0b"}], "methodology": {"title": "Our Proven Development Methodology", "description": "A 4‑phase approach from design thinking to production hardening, with clear acceptance criteria and rollout plans."}, "cta": {"title": "Ready to move forward?", "description": "Let’s finalize scope, timeline, and success metrics for Acme’s POC.", "buttonText": "Get Your Proposal"}}, "macroSections": [{"id": "overview", "title": "Project Overview", "icon": {"name": "FileText"}, "description": "Strategic context and value for Acme", "sections": [{"id": 1, "section_key": "executive-summary", "title": "Executive Summary", "icon": {"name": "FileText"}, "phase": "Design Thinking", "content": "**Project Vision**\n\nAcme’s Smart Sensor Dashboard will unify telemetry from heterogeneous devices, detect anomalies in near real‑time, and surface insights for operations and management.\n\n**Key Outcomes**\n\n• Centralized telemetry views and health indicators\n• Configurable alerts and exports (CSV/PDF)\n• Role‑based access for ops and leadership"}], "order": 1}, {"id": "solution", "title": "Solution Outline", "icon": {"name": "Settings"}, "description": "Technical approach and early scope", "sections": [{"id": 2, "section_key": "solution-architecture", "title": "Solution Architecture", "icon": {"name": "Server"}, "phase": "Prototype Phase", "content": "**Architecture**\n\n• Next.js app with API routes for ingest and query\n• Stream processing via managed queue/DB\n• PostHog for product analytics\n\n**Phase 1 Scope**\n\n• Device registry and simple health status\n• Basic alert rules and CSV export\n• Read‑only dashboard for leadership"}], "order": 2}]}