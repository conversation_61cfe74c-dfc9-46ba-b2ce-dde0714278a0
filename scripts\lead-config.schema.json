{
  "$schema": "https://json-schema.org/draft/2020-12/schema",
  "title": "LeadConfig",
  "type": "object",
  "properties": {
    "branding": {
      "type": "object",
      "properties": {
        "logo": {"type": "string", "description": "URL to company logo"},
        "accentColor": {"type": "string", "description": "Brand accent color"},
        "companyName": {"type": "string", "description": "Company name"},
        "companyDescription": {"type": "string", "description": "Short company description"}
      },
      "required": ["logo", "accentColor", "companyName", "companyDescription"],
      "additionalProperties": false
    },
    "variables": {
      "type": "object",
      "properties": {
        "projectName": {"type": "string"},
        "timeline": {"type": "string"},
        "budget": {"type": "string"},
        "expectedROI": {"type": "string"},
        "marketSize": {"type": "string"},
        "developmentWeeks": {"type": "string"}
      },
      "required": ["projectName", "timeline", "budget"],
      "additionalProperties": false
    },
    "content": {
      "type": "object",
      "properties": {
        "proposalUrl": {"type": "string", "format": "uri"},
        "hero": {
          "type": "object",
          "properties": {
            "title": {"type": "string"},
            "subtitle": {"type": "string"},
            "description": {"type": "string"},
            "primaryCTA": {"type": "string"},
            "secondaryCTA": {"type": "string"},
            "features": {"type": "array", "items": {"type": "string"}}
          },
          "required": ["title", "description", "primaryCTA"],
          "additionalProperties": false
        },
        "statistics": {
          "type": "array",
          "items": {
            "type": "object",
            "properties": {
              "id": {"type": "number"},
              "value": {"type": "string"},
              "label": {"type": "string"},
              "icon": {
                "type": "object",
                "properties": {
                  "name": {"type": "string"}
                },
                "required": ["name"],
                "additionalProperties": false
              },
              "color": {"type": "string"}
            },
            "required": ["id", "value", "label", "icon"],
            "additionalProperties": false
          }
        },
        "methodology": {
          "type": "object",
          "properties": {
            "title": {"type": "string"},
            "description": {"type": "string"}
          },
          "required": ["title", "description"],
          "additionalProperties": false
        },
        "cta": {
          "type": "object",
          "properties": {
            "title": {"type": "string"},
            "description": {"type": "string"},
            "buttonText": {"type": "string"}
          },
          "required": ["title", "description", "buttonText"],
          "additionalProperties": false
        }
      },
      "required": ["proposalUrl", "hero", "statistics", "methodology", "cta"],
      "additionalProperties": false
    },
    "macroSections": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "id": {"type": "string"},
          "title": {"type": "string"},
          "icon": {
            "type": "object",
            "properties": {
              "name": {"type": "string"}
            },
            "required": ["name"],
            "additionalProperties": false
          },
          "sections": {
            "type": "array",
            "items": {
              "type": "object",
              "properties": {
                "id": {"type": "number"},
                "section_key": {"type": "string"},
                "title": {"type": "string"},
                "icon": {
                  "type": "object",
                  "properties": {
                    "name": {"type": "string"}
                  },
                  "required": ["name"],
                  "additionalProperties": false
                },
                "phase": {"type": "string"},
                "content": {"type": "string"}
              },
              "required": ["id", "section_key", "title", "icon", "content"],
              "additionalProperties": false
            }
          },
        },
        "required": ["id", "title", "icon", "sections"],
        "additionalProperties": false
      }
    }
  },
  "required": ["branding", "variables", "content", "macroSections"],
  "additionalProperties": false
}
